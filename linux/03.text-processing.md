# Linux Text Processing Commands for DevOps

In Linux systems, text processing commands are essential tools for DevOps engineers, system administrators, and developers. These commands enable efficient log analysis, data extraction, system monitoring, and automation tasks. This guide covers the most important text processing commands with practical examples for real-world DevOps scenarios.

## grep - Pattern Matching and Searching

The `grep` command searches for patterns in files and is one of the most frequently used commands in DevOps for log analysis and troubleshooting.

### Basic Syntax

```bash
grep [OPTIONS] PATTERN [FILE...]
```

### Common Options

- `-i`: Ignore case
- `-r`: Recursive search in directories
- `-n`: Show line numbers
- `-v`: Invert match (show non-matching lines)
- `-E`: Extended regular expressions
- `-A NUM`: Print NUM lines after matching line
- `-B NUM`: Print NUM lines before matching line
- `-C NUM`: Print NUM lines before and after matching line

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Find error messages in application logs
grep -i "error" /var/log/application.log

# Find critical errors with context
grep -i -C 3 "critical" /var/log/application.log

# Search for specific error codes across multiple log files
grep -r "ERROR_500" /var/log/

# Find recent errors (last 100 lines of log)
tail -100 /var/log/application.log | grep -i "error"

# Count occurrences of specific errors
grep -c "timeout" /var/log/application.log
```

#### Security Breaches

```bash
# Find failed login attempts
grep "Failed password" /var/log/auth.log

# Search for suspicious IP addresses
grep "*************" /var/log/apache2/access.log

# Find potential SQL injection attempts
grep -i "select.*from" /var/log/apache2/access.log

# Look for unauthorized access attempts
grep -E "(unauthorized|forbidden)" /var/log/webserver.log

# Search for file inclusion attempts
grep -i "etc/passwd" /var/log/apache2/access.log
```

#### Performance Troubleshooting

```bash
# Find slow database queries
grep -i "slow query" /var/log/mysql.log

# Search for high CPU usage warnings
grep -i "high cpu" /var/log/system.log

# Find memory-related issues
grep -i "out of memory" /var/log/messages

# Look for connection timeout errors
grep "connection timeout" /var/log/application.log

# Find resource exhaustion events
grep -E "(oom|killed)" /var/log/messages
```

### Command Combinations

```bash
# Find errors and exclude debug messages
grep -i "error" /var/log/application.log | grep -v "debug"

# Find errors and show with timestamps
grep -n -i "error" /var/log/application.log

# Find errors and count occurrences by type
grep -i "error" /var/log/application.log | sort | uniq -c

# Find errors and extract specific fields (e.g., timestamps)
grep -i "error" /var/log/application.log | awk '{print $1, $2}'

# Find errors and save to a separate file
grep -i "error" /var/log/application.log > errors_found.log
```

## tail - Monitoring Log Files

The `tail` command displays the end of files and is particularly useful for monitoring log files in real-time.

### Basic Syntax

```bash
tail [OPTIONS] [FILE...]
```

### Common Options

- `-n NUM`: Show last NUM lines
- `-f`: Follow file as it grows
- `-F`: Follow file and reopen if rotated
- `--since TIME`: Show lines since specified time
- `--until TIME`: Show lines until specified time

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Monitor application logs in real-time
tail -f /var/log/application.log

# Monitor multiple log files simultaneously
tail -f /var/log/application.log /var/log/error.log

# Show last 100 lines and follow
tail -n 100 -f /var/log/application.log

# Monitor logs with grep filtering
tail -f /var/log/application.log | grep -i "error"

# Monitor logs and highlight errors (requires ccze or similar)
tail -f /var/log/application.log | grep --color=always -i "error\|warning\|critical"
```

#### Security Breaches

```bash
# Monitor authentication logs for suspicious activity
tail -f /var/log/auth.log | grep -i "failed"

# Monitor web server access logs
tail -f /var/log/apache2/access.log

# Monitor for specific attack patterns
tail -f /var/log/apache2/access.log | grep -E "(sqlmap|nikto|nessus)"

# Monitor firewall logs
tail -f /var/log/ufw.log

# Monitor SSH login attempts
tail -f /var/log/auth.log | grep "sshd"
```

#### Performance Troubleshooting

```bash
# Monitor system logs for performance issues
tail -f /var/log/syslog | grep -i "high"

# Monitor resource usage logs
tail -f /var/log/system.log | grep -E "(cpu|memory|disk)"

# Monitor database logs for slow queries
tail -f /var/log/mysql.log | grep -i "slow"

# Monitor application response times
tail -f /var/log/application.log | grep "response_time"

# Monitor for service restarts
tail -f /var/log/system.log | grep -i "restart"
```

### Command Combinations

```bash
# Monitor logs and extract specific fields
tail -f /var/log/application.log | awk '{print $1, $2, $NF}'

# Monitor logs and count occurrences
tail -f /var/log/application.log | grep -i "error" | tee >(wc -l)

# Monitor logs and send alerts (example with mail)
tail -f /var/log/application.log | grep -i "critical" | while read line; do echo "$line" | mail -s "Critical Error" <EMAIL>; done

# Monitor logs with timestamp filtering
tail -f /var/log/application.log | grep "$(date '+%Y-%m-%d')"

# Monitor logs and save to file while displaying
tail -f /var/log/application.log | tee /tmp/monitor.log
```

## awk - Text Processing and Field Extraction

The `awk` command is a powerful text processing tool that can manipulate data fields, perform calculations, and generate reports.

### Basic Syntax

```bash
awk [OPTIONS] 'pattern { action }' [FILE...]
```

### Common Options

- `-F`: Specify field separator
- `-v`: Assign variable
- `BEGIN`: Execute before processing input
- `END`: Execute after processing input

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Extract timestamps and error messages from logs
awk '/error|Error|ERROR/ {print $1, $2, $NF}' /var/log/application.log

# Find lines with high response times (assuming response time is in field 5)
awk '$5 > 5000 {print $0}' /var/log/access.log

# Count errors by hour
awk '/error/ {print $2}' /var/log/application.log | cut -d: -f1 | sort | uniq -c

# Extract specific fields from structured logs
awk -F',' '{print $1, $3, $5}' /var/log/application.csv

# Find top 10 IP addresses with errors
awk '/error/ {print $1}' /var/log/access.log | sort | uniq -c | sort -nr | head -10
```

#### Security Breaches

```bash
# Extract IP addresses from access logs
awk '{print $1}' /var/log/apache2/access.log | sort | uniq -c | sort -nr

# Find suspicious user agents
awk -F'"' '/POST/ {print $6}' /var/log/apache2/access.log | sort | uniq -c

# Analyze failed login attempts by IP
awk '/Failed/ {print $11}' /var/log/auth.log | sort | uniq -c | sort -nr

# Extract HTTP status codes and count occurrences
awk '{print $9}' /var/log/apache2/access.log | sort | uniq -c | sort -nr

# Find requests with large data transfers
awk '$10 > 1000000 {print $1, $7, $10}' /var/log/apache2/access.log
```

#### Performance Troubleshooting

```bash
# Calculate average response time
awk '{sum+=$5; count++} END {print "Average response time:", sum/count}' /var/log/access.log

# Find requests with response time greater than average
awk '{sum+=$5; count++} END {avg=sum/count; print "Average:", avg}' /var/log/access.log && awk -v avg=$(awk '{sum+=$5; count++} END {print sum/count}' /var/log/access.log) '$5 > avg {print $0}' /var/log/access.log

# Analyze resource usage from system logs
awk '/CPU|Memory/ {print $1, $2, $NF}' /var/log/system.log

# Extract and format performance data
awk -F',' 'NR > 1 {print $1, $2*100"%", $3"MB"}' /var/log/performance.csv

# Find peak usage times
awk '{print $2, $5}' /var/log/resource.log | sort -k2 -nr | head -10
```

### Command Combinations

```bash
# Process logs and generate summary reports
awk '/error/ {print $1, $2}' /var/log/application.log | sort | uniq -c | sort -nr | head -20

# Combine with other commands for complex processing
grep "error" /var/log/application.log | awk '{print $1, $NF}' | sort | uniq -c

# Process CSV data and calculate statistics
awk -F',' 'BEGIN {max=0} {if ($3>max) max=$3} END {print "Max value:", max}' data.csv

# Generate formatted output for reporting
awk 'BEGIN {print "Time\t\tError Count"} /error/ {print $2 "\t" $NF}' /var/log/application.log

# Process logs with conditional logic
awk '{if ($5 > 1000) print "High load at", $1, $2; else if ($5 > 500) print "Medium load at", $1, $2}' /var/log/system.log
```

## sort - Sorting Data

The `sort` command arranges lines of text files in various orders, which is useful for organizing log data and generating reports.

### Basic Syntax

```bash
sort [OPTIONS] [FILE...]
```

### Common Options

- `-n`: Numerical sort
- `-r`: Reverse order
- `-k NUM`: Sort by field NUM
- `-u`: Unique lines only
- `-t CHAR`: Specify field separator
- `-f`: Ignore case
- `-h`: Human-readable number sort (e.g., 1K, 2M)

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Sort error logs by timestamp
sort /var/log/error.log

# Sort numerical error codes
sort -n error_codes.txt

# Sort logs by severity (assuming severity is in field 3)
sort -k3 /var/log/application.log

# Find unique error messages
sort -u /var/log/error.log

# Sort by multiple fields (timestamp then severity)
sort -k1 -k3 /var/log/application.log
```

#### Security Breaches

```bash
# Sort IP addresses by frequency of appearance
grep "Failed" /var/log/auth.log | awk '{print $11}' | sort | uniq -c | sort -nr

# Sort access logs by response size
sort -k10 -n /var/log/apache2/access.log

# Sort security events by date
sort -k1 -k2 /var/log/security.log

# Sort login attempts by username
sort -k9 /var/log/auth.log

# Sort firewall logs by port
sort -k7 -n /var/log/ufw.log
```

#### Performance Troubleshooting

```bash
# Sort performance metrics by value
sort -k3 -n /var/log/performance.log

# Sort resource usage logs by timestamp
sort -k1 -k2 /var/log/resource.log

# Sort application logs by response time
sort -k5 -n /var/log/application.log

# Sort system logs by process ID
sort -k4 -n /var/log/system.log

# Sort database query logs by execution time
sort -k6 -n /var/log/database.log
```

### Command Combinations

```bash
# Sort and find unique entries
grep "error" /var/log/application.log | sort | uniq

# Sort and count occurrences
grep "error" /var/log/application.log | sort | uniq -c | sort -nr

# Sort numerically and get top 10
awk '{print $5}' /var/log/access.log | sort -n | tail -10

# Sort by field and format output
cut -d' ' -f1,3 /var/log/application.log | sort -k2 | column -t

# Sort with custom delimiter
sort -t',' -k3 -n data.csv
```

## sed - Stream Editing

The `sed` command is a stream editor for filtering and transforming text, useful for log processing and configuration file manipulation.

### Basic Syntax

```bash
sed [OPTIONS] 'command' [FILE...]
```

### Common Commands

- `s/pattern/replacement/`: Substitute pattern with replacement
- `d`: Delete line
- `p`: Print line
- `i`: Insert text before line
- `a`: Append text after line
- `c`: Change line

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Remove sensitive information from logs
sed 's/[0-9]\{4\}[0-9]\{4\}[0-9]\{4\}[0-9]\{4\}/XXXX-XXXX-XXXX-XXXX/g' /var/log/application.log

# Replace error messages with more descriptive ones
sed 's/ERROR 500/Internal Server Error - Database Connection Failed/g' /var/log/application.log

# Remove debug messages from logs
sed '/DEBUG/d' /var/log/application.log

# Add timestamp to log entries
sed 's/^/$(date): /' /var/log/application.log

# Extract only error lines
sed -n '/error/p' /var/log/application.log
```

#### Security Breaches

```bash
# Anonymize IP addresses in logs
sed 's/[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}/XXX.XXX.XXX.XXX/g' /var/log/access.log

# Remove or replace sensitive data
sed 's/password=[^ ]*/password=REDACTED/g' /var/log/application.log

# Filter out known good IPs
sed '/192\.168\.1\./d' /var/log/access.log

# Highlight suspicious patterns
sed 's/\(sqlmap\|nikto\)/[SUSPICIOUS] \1/g' /var/log/access.log

# Clean up malformed log entries
sed '/^\s*$/d' /var/log/security.log
```

#### Performance Troubleshooting

```bash
# Format performance data
sed 's/,/\t/g' /var/log/performance.csv > /var/log/performance.tsv

# Add labels to performance metrics
sed 's/^/CPU: /' cpu.log

# Normalize data formats
sed 's/\([0-9]\)ms/\1 milliseconds/' /var/log/response.log

# Remove unit identifiers for numerical processing
sed 's/%//g' /var/log/cpu.log | sed 's/MB//g'

# Standardize timestamp formats
sed 's/\([0-9]\{4\}\)-\([0-9]\{2\}\)-\([0-9]\{2\}\)/\2\/\3\/\1/' /var/log/application.log
```

### Command Combinations

```bash
# Chain multiple sed operations
cat /var/log/application.log | sed 's/error/ERROR/g' | sed 's/warning/WARNING/g'

# Combine with other commands
sed -n '/error/p' /var/log/application.log | sort | uniq -c

# In-place editing of configuration files
sed -i 's/old_value/new_value/g' /etc/application.conf

# Process multiple files
sed 's/pattern/replacement/g' /var/log/*.log

# Complex pattern replacement with grouping
sed 's/\(.*\)\(ERROR\)\(.*\)/\1[ALERT]\2\3/' /var/log/application.log
```

## cut - Field Extraction

The `cut` command extracts specific fields or characters from lines, useful for processing structured data like logs and CSV files.

### Basic Syntax

```bash
cut [OPTIONS] [FILE...]
```

### Common Options

- `-d CHAR`: Specify delimiter
- `-f NUM`: Select field(s)
- `-c NUM`: Select character(s)
- `-s`: Suppress lines without delimiters

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Extract timestamps from logs (assuming space delimiter)
cut -d' ' -f1-2 /var/log/application.log

# Extract error codes from structured logs
cut -d',' -f3 /var/log/errors.csv

# Extract specific fields from CSV data
cut -d',' -f1,3,5 /var/log/application.csv

# Extract characters from fixed-width logs
cut -c1-10,20-30 /var/log/system.log

# Extract URL paths from access logs
cut -d' ' -f7 /var/log/apache2/access.log
```

#### Security Breaches

```bash
# Extract IP addresses from access logs
cut -d' ' -f1 /var/log/apache2/access.log

# Extract user agents from access logs
cut -d'"' -f2 /var/log/apache2/access.log

# Extract usernames from authentication logs
cut -d' ' -f9 /var/log/auth.log

# Extract HTTP methods from access logs
cut -d' ' -f6 /var/log/apache2/access.log | tr -d '"'

# Extract file extensions from requested URLs
cut -d' ' -f7 /var/log/apache2/access.log | cut -d'.' -f2
```

#### Performance Troubleshooting

```bash
# Extract CPU usage values from system logs
cut -d' ' -f5 /var/log/cpu.log

# Extract memory usage from resource logs
cut -d',' -f3 /var/log/resource.csv

# Extract response times from application logs
cut -d' ' -f8 /var/log/application.log

# Extract process IDs from system logs
cut -d' ' -f4 /var/log/system.log

# Extract service names from logs
cut -d'[' -f2 /var/log/application.log | cut -d']' -f1
```

### Command Combinations

```bash
# Combine with other commands for field processing
grep "error" /var/log/application.log | cut -d' ' -f1-3

# Process multiple fields and sort
 cut -d',' -f1,3,5 /var/log/data.csv | sort -k3 -n

# Extract fields and count unique values
cut -d' ' -f1 /var/log/access.log | sort | uniq -c

# Process character ranges and filter
cut -c1-19 /var/log/application.log | grep "2023"

# Extract and format fields
cut -d' ' -f1,2,7 /var/log/access.log | column -t
```

## uniq - Removing Duplicates

The `uniq` command filters adjacent matching lines from input, useful for identifying unique events or counting occurrences.

### Basic Syntax

```bash
uniq [OPTIONS] [FILE...]
```

### Common Options

- `-c`: Prefix lines with count
- `-d`: Only print duplicate lines
- `-u`: Only print unique lines
- `-i`: Ignore case

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Count occurrences of error messages
sort /var/log/error.log | uniq -c | sort -nr

# Find repeated error patterns
sort /var/log/application.log | uniq -d

# Identify unique error types
sort /var/log/error.log | uniq

# Find one-time errors only
sort /var/log/application.log | uniq -u

# Count unique users experiencing errors
grep "error" /var/log/application.log | awk '{print $3}' | sort | uniq -c
```

#### Security Breaches

```bash
# Count unique IP addresses in access logs
awk '{print $1}' /var/log/apache2/access.log | sort | uniq -c | sort -nr

# Find repeated failed login attempts
grep "Failed" /var/log/auth.log | awk '{print $11}' | sort | uniq -c

# Identify unique attack patterns
grep -i "sqlmap\|nikto" /var/log/access.log | sort | uniq -c

# Find unique user agents
awk -F'"' '{print $6}' /var/log/apache2/access.log | sort | uniq -c

# Count unique error sources
grep "ERROR" /var/log/application.log | cut -d' ' -f1 | sort | uniq -c
```

#### Performance Troubleshooting

```bash
# Count unique high CPU processes
awk '$3 > 80 {print $2}' /var/log/cpu.log | sort | uniq -c

# Find unique slow queries
awk '$5 > 1000 {print $4}' /var/log/database.log | sort | uniq -c

# Identify unique resource bottlenecks
grep "high" /var/log/system.log | cut -d' ' -f4 | sort | uniq -c

# Count unique service restarts
grep "restart" /var/log/system.log | awk '{print $2}' | sort | uniq -c

# Find unique performance degradation patterns
awk '$5 > $6*1.5 {print $1, $2}' /var/log/performance.log | sort | uniq -c
```

### Command Combinations

```bash
# Sort, count, and filter
sort /var/log/error.log | uniq -c | awk '$1 > 10'

# Find duplicates and process further
grep "error" /var/log/application.log | sort | uniq -d | wc -l

# Count unique entries and format output
awk '{print $1}' /var/log/access.log | sort | uniq -c | sort -nr | head -20

# Process unique entries with additional filtering
sort /var/log/application.log | uniq -u | grep -v "debug"

# Combine with sed for complex processing
sort /var/log/application.log | uniq -c | sed 's/^\s*\([0-9]*\)\s*\(.*\)/\1 occurrences: \2/'
```

## head - Viewing File Beginnings

The `head` command displays the beginning of files, useful for quickly inspecting log formats and file structures.

### Basic Syntax

```bash
head [OPTIONS] [FILE...]
```

### Common Options

- `-n NUM`: Show first NUM lines
- `-c NUM`: Show first NUM bytes
- `-q`: Never print file names

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Check log file format
head -10 /var/log/application.log

# View recent log entries (when log is rotated)
head -20 /var/log/application.log.1

# Check configuration file structure
head -n 50 /etc/application.conf

# Inspect CSV header structure
head -1 /var/log/application.csv

# View beginning of large log files quickly
head -n 1000 /var/log/large-application.log | grep "error"
```

#### Security Breaches

```bash
# Check log format for parsing
head -5 /var/log/auth.log

# Inspect firewall log structure
head -10 /var/log/ufw.log

# View beginning of security audit logs
head -20 /var/log/audit.log

# Check web server log format
head -5 /var/log/apache2/access.log

# Inspect system log headers
head -3 /var/log/syslog
```

#### Performance Troubleshooting

```bash
# Check performance log format
head -10 /var/log/performance.log

# View system resource log structure
head -15 /var/log/system.log

# Inspect database log headers
head -5 /var/log/mysql.log

# Check monitoring data format
head -3 /var/log/monitoring.csv

# View application metrics file structure
head -n 50 /var/log/metrics.log
```

### Command Combinations

```bash
# Combine with other commands for quick inspection
grep "error" /var/log/application.log | head -10

# Process first part of file differently
cat /var/log/application.log | { head -100 | grep "error"; tail -n +101 | grep "warning"; }

# Extract header information
head -1 /var/log/application.csv | tr ',' '\n'

# Compare file beginnings
diff <(head -10 file1.log) <(head -10 file2.log)

# Process with line numbers
head -20 /var/log/application.log | nl
```

## wc - Word/Line Counting

The `wc` command counts lines, words, and characters in files, useful for log analysis and data validation.

### Basic Syntax

```bash
wc [OPTIONS] [FILE...]
```

### Common Options

- `-l`: Count lines
- `-w`: Count words
- `-c`: Count bytes
- `-m`: Count characters

### Practical Examples for DevOps Scenarios

#### Production Incidents

```bash
# Count total error log entries
wc -l /var/log/error.log

# Count words in error descriptions
wc -w /var/log/error.log

# Check log file size in bytes
wc -c /var/log/application.log

# Count lines with specific errors
grep -i "error" /var/log/application.log | wc -l

# Validate log rotation (check expected number of lines)
wc -l /var/log/application.log.*
```

#### Security Breaches

```bash
# Count total access log entries
wc -l /var/log/apache2/access.log

# Count failed login attempts
grep "Failed" /var/log/auth.log | wc -l

# Count suspicious requests
grep -E "(sqlmap|nikto)" /var/log/access.log | wc -l

# Check firewall log size
wc -l /var/log/ufw.log

# Count security events
wc -l /var/log/security.log
```

#### Performance Troubleshooting

```bash
# Count performance metric entries
wc -l /var/log/performance.log

# Count resource usage records
wc -l /var/log/resource.log

# Check database query log size
wc -l /var/log/database.log

# Count system monitoring entries
wc -l /var/log/system.log

# Validate data collection completeness
wc -l /var/log/metrics.log
```

### Command Combinations

```bash
# Count and compare log sizes
wc -l /var/log/*.log

# Process multiple files and format output
wc -l /var/log/application.log* | head -n -1

# Count with grep filtering
grep "error" /var/log/application.log | wc -l

# Combine with other text processing
grep "error" /var/log/application.log | sort | uniq -c | wc -l

# Process and format counts
wc -l /var/log/*.log | awk '{print $2":", $1" lines"}'
```

## Best Practices for Production Use

1. **Use appropriate buffering**: When monitoring logs with `tail -f`, consider using `stdbuf` to control buffering
2. **Handle large files efficiently**: Use `--line-buffered` with grep for large files
3. **Combine commands effectively**: Use pipes to chain commands, but avoid unnecessary complexity
4. **Test patterns carefully**: Always test regex patterns before applying to production logs
5. **Consider performance impact**: Complex text processing on large files can impact system performance
6. **Use file rotation awareness**: Use `tail -F` instead of `tail -f` for logs that may be rotated
7. **Validate output**: Always verify that your text processing commands produce expected results
8. **Document complex commands**: Save complex processing pipelines as scripts with comments

## Summary

Linux text processing commands are indispensable tools for DevOps engineers. Mastering `grep`, `tail`, `awk`, `sort`, `sed`, `cut`, `uniq`, `head`, and `wc` enables efficient log analysis, system monitoring, and troubleshooting. These commands become even more powerful when combined through pipes, allowing complex data processing workflows that are essential for modern DevOps practices. Regular practice with these commands in various scenarios will improve your ability to quickly diagnose and resolve issues in production environments.
