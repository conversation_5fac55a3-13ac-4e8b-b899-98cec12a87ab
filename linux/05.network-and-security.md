# Network and Security in Linux

This guide covers essential network and security concepts in Linux, focusing on SSH for remote access and firewall configuration.

## SSH and Remote Access

### SSH Basics and Configuration

SSH (Secure Shell) is a cryptographic network protocol that provides secure access to remote systems. It's the standard method for securely managing Linux servers remotely.

#### SSH Configuration Files

- **Global configuration**: `/etc/ssh/sshd_config` (server) and `/etc/ssh/ssh_config` (client)
- **User configuration**: `~/.ssh/config`
- **Authorized keys**: `~/.ssh/authorized_keys`
- **Known hosts**: `~/.ssh/known_hosts`

#### Essential SSH Server Configuration Options

```bash
# Edit the SSH daemon configuration
sudo nano /etc/ssh/sshd_config

# Key configuration options:
Port 22
PermitRootLogin no
PasswordAuthentication yes
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PermitEmptyPasswords no
X11Forwarding yes
PrintMotd no
TCPKeepAlive yes
ClientAliveInterval 60
UsePAM yes
```

After making changes to the SSH configuration, restart the service:

```bash
# For systemd-based systems
sudo systemctl restart sshd

# For older systems
sudo service ssh restart
```

### Key-Based Authentication Setup

Key-based authentication is more secure than password authentication and enables automated processes.

#### Generating SSH Key Pairs

```bash
# Generate a new RSA key pair (4096 bits)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Generate an Ed25519 key (recommended for modern systems)
ssh-keygen -t ed25519 -C "<EMAIL>"

# Generate with a specific filename
ssh-keygen -t rsa -b 4096 -f ~/.ssh/myserver_key
```

#### Copying Public Key to Remote Server

```bash
# Using ssh-copy-id (easiest method)
ssh-copy-id username@remote_host

# Using SSH
cat ~/.ssh/id_rsa.pub | ssh username@remote_host "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"

# Manual copy
scp ~/.ssh/id_rsa.pub username@remote_host:~/.ssh/authorized_keys
```

#### Securing Private Keys

```bash
# Set proper permissions
chmod 700 ~/.ssh
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub
chmod 600 ~/.ssh/authorized_keys
chmod 644 ~/.ssh/known_hosts
```

### SSH Security Best Practices

1. **Disable root login**:
   ```bash
   PermitRootLogin no
   ```

2. **Use non-standard ports**:
   ```bash
   Port 2222
   ```

3. **Disable password authentication** (after setting up key-based auth):
   ```bash
   PasswordAuthentication no
   ```

4. **Limit user access**:
   ```bash
   AllowUsers user1 user2
   AllowGroups sshusers
   ```

5. **Implement connection timeouts**:
   ```bash
   ClientAliveInterval 300
   ClientAliveCountMax 2
   ```

6. **Use fail2ban for brute force protection**:
   ```bash
   sudo apt install fail2ban
   ```

### Common SSH Commands and Options

#### Basic Connection

```bash
# Simple connection
ssh username@hostname

# Specify port
ssh -p 2222 username@hostname

# Verbose output for debugging
ssh -v username@hostname

# Force specific SSH version
ssh -1 username@hostname  # SSH v1
ssh -2 username@hostname  # SSH v2
```

#### File Transfer

```bash
# Copy file to remote server
scp file.txt username@hostname:/path/to/destination

# Copy file from remote server
scp username@hostname:/path/to/file.txt .

# Copy directory recursively
scp -r directory/ username@hostname:/path/to/destination

# Preserve file attributes
scp -p file.txt username@hostname:/path/
```

#### Advanced SSH Options

```bash
# Use specific identity file
ssh -i ~/.ssh/my_key username@hostname

# Forward X11 applications
ssh -X username@hostname

# Forward agent (use with caution)
ssh -A username@hostname

# Local port forwarding
ssh -L 8080:localhost:80 username@hostname

# Dynamic port forwarding (SOCKS proxy)
ssh -D 1080 username@hostname
```

### SSH Tunneling and Port Forwarding

SSH tunneling allows you to secure traffic between systems and access services that are not publicly exposed.

#### Local Port Forwarding (-L)

Forwards a local port to a remote server:

```bash
# Access a remote database locally
ssh -L 3306:localhost:3306 username@remote-server

# Access a web service running on a remote internal server
ssh -L 8080:internal-server:80 username@remote-server
```

#### Remote Port Forwarding (-R)

Forwards a remote port to your local machine:

```bash
# Allow remote access to a local service
ssh -R 8080:localhost:80 username@remote-server

# Access a service behind NAT
ssh -R 2222:localhost:22 username@public-server
```

#### Dynamic Port Forwarding (-D)

Creates a SOCKS proxy for dynamic port forwarding:

```bash
# Create SOCKS proxy on port 1080
ssh -D 1080 username@remote-server

# Configure browser to use SOCKS proxy at localhost:1080
```

### Managing SSH Keys and authorized_keys

#### SSH Key Management

```bash
# List available keys
ls -la ~/.ssh/

# Add key to ssh-agent
ssh-add ~/.ssh/id_rsa

# List loaded keys
ssh-add -l

# Remove all keys from agent
ssh-add -D
```

#### authorized_keys File Format

Each line in `~/.ssh/authorized_keys` contains one public key with optional options:

```bash
# Simple key
ssh-rsa AAAAB3NzaC1yc2E... user@host

# Key with options
command="echo 'This account can only echo'",no-port-forwarding ssh-rsa AAAAB3NzaC1yc2E... user@host

# Key with environment variable
environment="EDITOR=vim" ssh-rsa AAAAB3NzaC1yc2E... user@host
```

#### Common authorized_keys Options

- `command="command"`: Restrict key to run only specified command
- `no-port-forwarding`: Disable port forwarding
- `no-X11-forwarding`: Disable X11 forwarding
- `from="pattern-list"`: Restrict source addresses
- `environment="VAR=value"`: Set environment variables

### Troubleshooting SSH Connections

#### Common Issues and Solutions

1. **Permission denied (publickey)**:
   ```bash
   # Check permissions
   chmod 700 ~/.ssh
   chmod 600 ~/.ssh/authorized_keys
   
   # Verify key format
   cat ~/.ssh/authorized_keys
   
   # Check SSH logs
   sudo tail -f /var/log/auth.log
   ```

2. **Connection refused**:
   ```bash
   # Check if SSH service is running
   sudo systemctl status ssh
   
   # Check listening ports
   netstat -tlnp | grep :22
   
   # Check firewall rules
   sudo ufw status
   ```

3. **Host key verification failed**:
   ```bash
   # Remove old host key
   ssh-keygen -R hostname
   
   # Or remove from known_hosts manually
   sed -i '/hostname/d' ~/.ssh/known_hosts
   ```

#### SSH Debugging Commands

```bash
# Verbose connection output
ssh -v username@hostname
ssh -vv username@hostname  # More verbose
ssh -vvv username@hostname  # Most verbose

# Test SSH configuration
sudo sshd -t

# Check SSH service logs
sudo journalctl -u ssh

# Monitor authentication logs
sudo tail -f /var/log/auth.log
```

## Firewall Configuration

### Introduction to Linux Firewalls

Linux provides several firewall solutions, each with different features and complexity levels:

#### iptables

The traditional Linux packet filtering framework:
- Direct interface to the kernel's netfilter
- Complex rule syntax
- Available on most Linux distributions
- Requires manual rule persistence

#### ufw (Uncomplicated Firewall)

A simplified frontend for iptables:
- User-friendly command syntax
- Ideal for desktops and basic servers
- Available on Ubuntu and Debian-based systems
- Automatic rule persistence

#### firewalld

A dynamic firewall manager:
- Zone-based configuration
- Runtime and permanent rule management
- D-Bus interface
- Default on RHEL/CentOS 7+ and Fedora

### Basic Firewall Rules and Policies

#### ufw Basic Commands

```bash
# Enable/disable firewall
sudo ufw enable
sudo ufw disable

# Check status
sudo ufw status
sudo ufw status verbose
sudo ufw status numbered

# Set default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow services by name
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https

# Allow specific ports
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow port ranges
sudo ufw allow 60000:61000/udp

# Deny connections
sudo ufw deny from *************
```

#### iptables Basic Commands

```bash
# List current rules
sudo iptables -L
sudo iptables -L -v  # Verbose output
sudo iptables -L -n  # Numeric output

# Set default policies
sudo iptables -P INPUT DROP
sudo iptables -P FORWARD DROP
sudo iptables -P OUTPUT ACCEPT

# Allow loopback traffic
sudo iptables -A INPUT -i lo -j ACCEPT
sudo iptables -A OUTPUT -o lo -j ACCEPT

# Allow established connections
sudo iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# Allow specific ports
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

#### firewalld Basic Commands

```bash
# Check status
sudo firewall-cmd --state
sudo firewall-cmd --list-all

# Enable/disable service
sudo systemctl enable firewalld
sudo systemctl start firewalld

# Get default zone
sudo firewall-cmd --get-default-zone

# List available zones
sudo firewall-cmd --get-zones

# Allow services
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https

# Allow ports
sudo firewall-cmd --permanent --add-port=8080/tcp

# Reload configuration
sudo firewall-cmd --reload
```

### Opening and Closing Ports

#### Opening Ports

With ufw:
```bash
# Allow specific port
sudo ufw allow 8080

# Allow port with protocol
sudo ufw allow 8080/tcp

# Allow port range
sudo ufw allow 6000:6010/tcp

# Allow from specific IP
sudo ufw allow from ************* to any port 22

# Allow from subnet
sudo ufw allow from ***********/24 to any port 22
```

With iptables:
```bash
# Allow incoming TCP on port 80
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT

# Allow from specific IP
sudo iptables -A INPUT -p tcp -s ************* --dport 22 -j ACCEPT

# Allow from subnet
sudo iptables -A INPUT -p tcp -s ***********/24 --dport 22 -j ACCEPT
```

With firewalld:
```bash
# Add port permanently
sudo firewall-cmd --permanent --add-port=8080/tcp

# Add port to specific zone
sudo firewall-cmd --permanent --zone=public --add-port=8080/tcp

# Add port range
sudo firewall-cmd --permanent --add-port=8080-8090/tcp
```

#### Closing Ports

With ufw:
```bash
# Deny specific port
sudo ufw deny 8080

# Delete rule by number
sudo ufw delete 3

# Delete rule by specification
sudo ufw delete allow 8080
```

With iptables:
```bash
# Delete rule by line number
sudo iptables -L --line-numbers
sudo iptables -D INPUT 3

# Delete by specification
sudo iptables -D INPUT -p tcp --dport 8080 -j ACCEPT
```

With firewalld:
```bash
# Remove port
sudo firewall-cmd --permanent --remove-port=8080/tcp

# Remove service
sudo firewall-cmd --permanent --remove-service=http
```

### Network Address Translation (NAT)

NAT allows multiple devices to share a single public IP address.

#### iptables NAT Configuration

```bash
# Enable IP forwarding
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward

# Make permanent
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf

# Configure masquerading (source NAT)
sudo iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE

# Destination NAT (port forwarding)
sudo iptables -t nat -A PREROUTING -p tcp --dport 80 -j DNAT --to-destination *************:80

# Save rules (varies by distribution)
sudo iptables-save > /etc/iptables/rules.v4
```

#### firewalld NAT Configuration

```bash
# Enable masquerade
sudo firewall-cmd --permanent --add-masquerade

# Port forwarding
sudo firewall-cmd --permanent --add-forward-port=port=80:proto=tcp:toport=8080:toaddr=*************

# Reload configuration
sudo firewall-cmd --reload
```

### Firewall Logging and Monitoring

#### ufw Logging

```bash
# Enable/disable logging
sudo ufw logging on
sudo ufw logging off

# Set log level
sudo ufw logging low
sudo ufw logging medium
sudo ufw logging high
sudo ufw logging full

# View logs
sudo tail -f /var/log/ufw.log
```

#### iptables Logging

```bash
# Log dropped packets
sudo iptables -A INPUT -m limit --limit 5/min -j LOG --log-prefix "iptables denied: " --log-level 7

# Log specific traffic
sudo iptables -A INPUT -p tcp --dport 22 -j LOG --log-prefix "SSH attempt: "

# View logs
sudo tail -f /var/log/messages
sudo tail -f /var/log/syslog
```

#### firewalld Logging

```bash
# Enable panic mode logging
sudo firewall-cmd --set-log-denied=all

# View logs
sudo journalctl -u firewalld
```

### Common Firewall Configurations for Different Services

#### Web Server Configuration

With ufw:
```bash
sudo ufw allow 'Nginx Full'
sudo ufw allow 'Apache Full'
# Or manually:
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
```

With iptables:
```bash
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

With firewalld:
```bash
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
```

#### Database Server Configuration

For MySQL/MariaDB:
```bash
# Allow local access only
# No firewall changes needed

# Allow remote access (secure carefully)
sudo ufw allow from ***********/24 to any port 3306
```

For PostgreSQL:
```bash
# Allow access from specific network
sudo ufw allow from ***********/24 to any port 5432
```

#### DNS Server Configuration

```bash
# DNS (bind)
sudo ufw allow 53

# DNS with specific protocols
sudo ufw allow 53/tcp
sudo ufw allow 53/udp
```

#### Email Server Configuration

```bash
# SMTP
sudo ufw allow 25/tcp

# SMTPS
sudo ufw allow 465/tcp

# Submission
sudo ufw allow 587/tcp

# POP3
sudo ufw allow 110/tcp

# POP3S
sudo ufw allow 995/tcp

# IMAP
sudo ufw allow 143/tcp

# IMAPS
sudo ufw allow 993/tcp
```

### Security Considerations and Best Practices

#### General Firewall Best Practices

1. **Default deny policy**:
   ```bash
   # ufw
   sudo ufw default deny incoming
   sudo ufw default allow outgoing
   
   # iptables
   sudo iptables -P INPUT DROP
   sudo iptables -P FORWARD DROP
   sudo iptables -P OUTPUT ACCEPT
   
   # firewalld
   Default zone should have restricted settings
   ```

2. **Minimize exposed services**:
   - Only open ports that are absolutely necessary
   - Use specific IP restrictions when possible
   - Regularly audit open ports

3. **Rate limiting**:
   ```bash
   # ufw rate limiting
   sudo ufw limit ssh/tcp
   
   # iptables rate limiting
   sudo iptables -A INPUT -p tcp --dport 22 -m limit --limit 3/min --limit-burst 3 -j ACCEPT
   sudo iptables -A INPUT -p tcp --dport 22 -j DROP
   ```

4. **Connection tracking**:
   ```bash
   # Allow established connections
   sudo iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
   
   # firewalld connection tracking
   sudo firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="***********/24" port protocol="tcp" port="22" accept'
   ```

#### Advanced Security Configurations

1. **Fail2ban integration**:
   ```bash
   # Install fail2ban
   sudo apt install fail2ban
   
   # Configure jail.local
   sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
   
   # Enable SSH protection
   echo -e "[sshd]\nenabled = true\nport = ssh\nfilter = sshd\naction = iptables[name=SSH, port=ssh, protocol=tcp]\nlogpath = /var/log/auth.log\nmaxretry = 3\nbantime = 3600" | sudo tee -a /etc/fail2ban/jail.local
   
   # Restart fail2ban
   sudo systemctl restart fail2ban
   ```

2. **Geoblocking with iptables**:
   ```bash
   # Block IP ranges from specific countries (example for China)
   sudo iptables -A INPUT -s *******/8 -j DROP
   sudo iptables -A INPUT -s ********/8 -j DROP
   sudo iptables -A INPUT -s ********/8 -j DROP
   sudo iptables -A INPUT -s ********/7 -j DROP
   sudo iptables -A INPUT -s ********/8 -j DROP
   sudo iptables -A INPUT -s ********/8 -j DROP
   sudo iptables -A INPUT -s ********/8 -j DROP
   sudo iptables -A INPUT -s ********/8 -j DROP
   sudo iptables -A INPUT -s ********/8 -j DROP
   sudo iptables -A INPUT -s 60.0.0.0/8 -j DROP
   sudo iptables -A INPUT -s 6*******/8 -j DROP
   sudo iptables -A INPUT -s 10*******/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s 110.0.0.0/8 -j DROP
   sudo iptables -A INPUT -s 11*******/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s 1********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s 120.0.0.0/8 -j DROP
   sudo iptables -A INPUT -s 12*******/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s 17*******/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s 180.0.0.0/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s 210.0.0.0/8 -j DROP
   sudo iptables -A INPUT -s 21*******/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s 220.0.0.0/8 -j DROP
   sudo iptables -A INPUT -s 22*******/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   sudo iptables -A INPUT -s *********/8 -j DROP
   ```

3. **Port knocking**:
   ```bash
   # Install knockd
   sudo apt install knockd
   
   # Configure /etc/knockd.conf
   cat << EOF | sudo tee /etc/knockd.conf
   [options]
   logfile = /var/log/knockd.log
   
   [openSSH]
   sequence = 7000,8000,9000
   seq_timeout = 15
   command = /sbin/iptables -I INPUT -s %IP% -p tcp --dport 22 -j ACCEPT
   tcpflags = syn
   
   [closeSSH]
   sequence = 9000,8000,7000
   seq_timeout = 15
   command = /sbin/iptables -D INPUT -s %IP% -p tcp --dport 22 -j ACCEPT
   tcpflags = syn
   EOF
   
   # Start knockd
   sudo systemctl enable knockd
   sudo systemctl start knockd
   
   # Client-side knocking
   knock your-server-ip 7000 8000 9000
   ```

#### Monitoring and Maintenance

1. **Regular audits**:
   ```bash
   # List all open ports
   sudo netstat -tlnp
   
   # Check ufw status
   sudo ufw status numbered
   
   # List iptables rules
   sudo iptables -L -v
   
   # List firewalld rules
   sudo firewall-cmd --list-all
   ```

2. **Log analysis**:
   ```bash
   # Check for blocked connections
   sudo grep "UFW BLOCK" /var/log/ufw.log
   
   # Analyze iptables logs
   sudo grep "iptables denied" /var/log/messages
   
   # Check firewalld logs
   sudo journalctl -u firewalld --since today
   ```

3. **Backup configurations**:
   ```bash
   # ufw
   sudo cp -r /etc/ufw /etc/ufw.backup
   
   # iptables
   sudo iptables-save > /etc/iptables/rules.backup
   
   # firewalld
   sudo cp -r /etc/firewalld /etc/firewalld.backup
   ```

This comprehensive guide covers the essential aspects of SSH and firewall configuration in Linux. By following these practices, you can significantly improve the security posture of your Linux systems while maintaining necessary access for administration and services.