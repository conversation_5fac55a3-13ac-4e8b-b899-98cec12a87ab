# Essential Linux Commands for DevOps Practitioners

Linux commands are the foundation of DevOps practices, enabling system administration, automation, monitoring, and troubleshooting. This guide covers the essential commands every DevOps engineer should master, with practical examples for real-world scenarios.

## File and Directory Operations

### ls - List Directory Contents

The `ls` command lists files and directories in the current or specified directory.

#### Basic Syntax

```bash
ls [OPTIONS] [FILE/DIRECTORY]
```

#### Common Options

- `-l`: Long format with detailed information
- `-a`: Include hidden files
- `-h`: Human-readable file sizes
- `-t`: Sort by modification time
- `-r`: Reverse order
- `-R`: Recursive listing

#### DevOps Examples

```bash
# View detailed directory contents in human-readable format
ls -lah

# List files sorted by modification time (newest first)
ls -lt

# In CI/CD pipelines, check if build artifacts were created
if [ $(ls -1 build/*.jar 2>/dev/null | wc -l) -gt 0 ]; then
  echo "Build artifacts found"
else
  echo "No build artifacts found"
  exit 1
fi

# In container management, verify application files
ls -la /app/src/

# For log analysis, list log files by modification time
ls -lt /var/log/
```

### cd - Change Directory

The `cd` command changes the current working directory.

#### Basic Syntax

```bash
cd [DIRECTORY]
```

#### Common Usage Patterns

```bash
# Change to home directory
cd ~

# Change to previous directory
cd -

# Change to root directory
cd /

# In automation scripts, navigate to project directory
cd /opt/myapp && ./start.sh

# In CI/CD pipelines, navigate to build directory
cd $BUILD_DIR
```

### mkdir - Create Directories

The `mkdir` command creates new directories.

#### Basic Syntax

```bash
mkdir [OPTIONS] DIRECTORY...
```

#### Common Options

- `-p`: Create parent directories as needed
- `-m`: Set directory permissions

#### DevOps Examples

```bash
# Create directory structure for application logs
mkdir -p /var/log/myapp/{error,access,debug}

# In deployment scripts, create necessary directories
mkdir -p /opt/myapp/{config,data,logs}

# In container builds, create application directory
mkdir -p /app/src

# Create directory with specific permissions
mkdir -m 755 /shared/data
```

### rm - Remove Files and Directories

The `rm` command deletes files and directories.

#### Basic Syntax

```bash
rm [OPTIONS] FILE...
```

#### Common Options

- `-f`: Force removal without confirmation
- `-r`: Remove directories recursively
- `-i`: Interactive mode (prompt before removal)

#### DevOps Examples

```bash
# Clean up temporary files in automation scripts
rm -f /tmp/*.tmp

# Remove old log files (older than 7 days)
find /var/log/myapp -name "*.log" -mtime +7 -delete

# In CI/CD pipelines, clean workspace before build
rm -rf build/

# Remove directory and contents forcefully
rm -rf /opt/old-version/
```

### cp - Copy Files and Directories

The `cp` command copies files and directories.

#### Basic Syntax

```bash
cp [OPTIONS] SOURCE DEST
```

#### Common Options

- `-r`: Copy directories recursively
- `-p`: Preserve file attributes
- `-v`: Verbose output
- `-a`: Archive mode (equivalent to -dpR)

#### DevOps Examples

```bash
# Backup configuration files before updates
cp -p /etc/myapp.conf /etc/myapp.conf.backup

# Copy application files to deployment directory
cp -r src/* /opt/myapp/

# In CI/CD pipelines, copy artifacts to staging area
cp build/*.jar /staging/artifacts/

# Copy files while preserving permissions and timestamps
cp -a /opt/myapp /opt/myapp-backup
```

### mv - Move/Rename Files and Directories

The `mv` command moves or renames files and directories.

#### Basic Syntax

```bash
mv [OPTIONS] SOURCE DEST
```

#### DevOps Examples

```bash
# Rename log files with timestamp
mv app.log app.log.$(date +%Y%m%d)

# Move files to archive directory
mv *.log /var/log/archive/

# In deployment, switch versions
mv /opt/myapp /opt/myapp-old
mv /opt/myapp-new /opt/myapp

# Move build artifacts to deployment directory
mv target/*.jar /opt/deployment/
```

### find - Search for Files and Directories

The `find` command searches for files and directories based on various criteria.

#### Basic Syntax

```bash
find [PATH] [OPTIONS] [EXPRESSION]
```

#### Common Options

- `-name`: Search by filename pattern
- `-type`: Search by file type (f=file, d=directory)
- `-mtime`: Search by modification time
- `-size`: Search by file size
- `-exec`: Execute command on found items

#### DevOps Examples

```bash
# Find log files modified in the last 24 hours
find /var/log -name "*.log" -mtime -1

# Find large files (>100MB) for disk space management
find / -type f -size +100M -exec ls -lh {} \;

# In CI/CD, find and delete temporary files
find . -name "*.tmp" -delete

# Find configuration files and change permissions
find /etc/myapp -name "*.conf" -exec chmod 600 {} \;

# Find and archive old log files
find /var/log -name "*.log" -mtime +30 -exec gzip {} \;
```

### grep - Search for Patterns in Files

The `grep` command searches for patterns within files.

#### Basic Syntax

```bash
grep [OPTIONS] PATTERN [FILE...]
```

#### Common Options

- `-r`: Recursive search
- `-i`: Case insensitive
- `-n`: Show line numbers
- `-v`: Invert match
- `-E`: Extended regular expressions
- `-A NUM`: Show NUM lines after match
- `-B NUM`: Show NUM lines before match

#### DevOps Examples

```bash
# Search for error messages in log files
grep -i "error" /var/log/application.log

# Find failed login attempts in system logs
grep "Failed password" /var/log/auth.log

# In CI/CD, check for TODO comments in code
grep -r "TODO" src/

# Search for specific IP addresses in access logs
grep "*************" /var/log/nginx/access.log

# Find configuration parameters and their line numbers
grep -n "^port=" /etc/myapp.conf

# Search for critical errors with context
grep -i -A 5 -B 5 "critical" /var/log/system.log
```

## Text Processing Commands

### cat - Concatenate and Display Files

The `cat` command displays file contents and concatenates files.

#### Basic Syntax

```bash
cat [OPTIONS] [FILE...]
```

#### DevOps Examples

```bash
# Display configuration file contents
cat /etc/myapp.conf

# Create a simple configuration file
cat > /etc/myapp.conf << EOF
server.port=8080
server.host=localhost
log.level=INFO
EOF

# In CI/CD, combine multiple log files
cat build.log test.log deploy.log > full.log

# Display file with line numbers
cat -n script.sh
```

### less - View File Contents Page by Page

The `less` command displays file contents one page at a time.

#### Basic Syntax

```bash
less [FILE]
```

#### DevOps Examples

```bash
# View large log files
less /var/log/system.log

# View command output page by page
ps aux | less

# Search within file while viewing (use /pattern)
less /var/log/application.log

# View multiple files
less file1.log file2.log
```

### head - Display Beginning of Files

The `head` command displays the beginning of files.

#### Basic Syntax

```bash
head [OPTIONS] [FILE...]
```

#### Common Options

- `-n NUM`: Display first NUM lines
- `-c NUM`: Display first NUM bytes

#### DevOps Examples

```bash
# View first 10 lines of log file
head /var/log/application.log

# View first 20 lines of large file
head -n 20 bigfile.log

# In monitoring scripts, check latest log entries
tail -f /var/log/application.log | head -n 100

# View headers of HTTP response
head -n 10 http-response.txt
```

### tail - Display End of Files

The `tail` command displays the end of files.

#### Basic Syntax

```bash
tail [OPTIONS] [FILE...]
```

#### Common Options

- `-n NUM`: Display last NUM lines
- `-f`: Follow file as it grows
- `-c NUM`: Display last NUM bytes

#### DevOps Examples

```bash
# Monitor application logs in real-time
tail -f /var/log/application.log

# View last 50 lines of log file
tail -n 50 /var/log/system.log

# In container environments, monitor application logs
tail -f /app/logs/app.log

# Monitor multiple log files
tail -f /var/log/*.log

# View logs with line numbers
tail -n 20 /var/log/application.log | nl
```

### awk - Pattern Scanning and Processing

The `awk` command processes text files line by line.

#### Basic Syntax

```bash
awk [OPTIONS] 'program' [FILE...]
```

#### DevOps Examples

```bash
# Extract specific columns from log files
awk '{print $1, $4}' /var/log/access.log

# Find lines with specific pattern
awk '/ERROR/ {print $0}' /var/log/application.log

# Calculate sum of values in a column
awk '{sum += $3} END {print sum}' data.txt

# Filter log entries by date
awk '/2023-01-01/ {print}' /var/log/system.log

# Count occurrences of IP addresses
awk '{print $1}' /var/log/access.log | sort | uniq -c | sort -nr
```

### sed - Stream Editor

The `sed` command performs text transformations.

#### Basic Syntax

```bash
sed [OPTIONS] 'script' [FILE...]
```

#### DevOps Examples

```bash
# Replace text in configuration files
sed -i 's/old-value/new-value/g' /etc/myapp.conf

# Remove comment lines from configuration
sed '/^#/d' /etc/myapp.conf

# Add prefix to each line
sed 's/^/PREFIX: /' file.txt

# Extract specific lines
sed -n '10,20p' largefile.log

# In CI/CD, update version numbers
sed -i 's/version=.*/version=1.2.3/g' config.properties
```

## System Monitoring Commands

### ps - Process Status

The `ps` command displays information about running processes.

#### Basic Syntax

```bash
ps [OPTIONS]
```

#### Common Options

- `aux`: All processes for all users
- `ef`: Full format listing
- `--sort`: Sort by specified column

#### DevOps Examples

```bash
# View all running processes
ps aux

# Find specific process by name
ps aux | grep nginx

# Sort processes by CPU usage
ps aux --sort=-%cpu | head -10

# In monitoring scripts, check if service is running
if ps aux | grep -q '[n]ginx'; then
  echo "Nginx is running"
else
  echo "Nginx is not running"
fi

# Find processes using specific port
ps aux | grep $(lsof -t -i:8080)
```

### top - Display System Processes

The `top` command displays real-time system process information.

#### Basic Syntax

```bash
top [OPTIONS]
```

#### DevOps Examples

```bash
# Monitor system resources in real-time
top

# Run top once and exit
top -n 1

# Monitor specific user processes
top -u www-data

# In automation scripts, capture system snapshot
top -b -n 1 > system-snapshot.txt
```

### htop - Interactive Process Viewer

The `htop` command provides an interactive process viewer.

#### Basic Syntax

```bash
htop [OPTIONS]
```

#### DevOps Examples

```bash
# Interactive system monitoring
htop

# Colorful process view with tree structure
htop -t

# Filter processes by name
htop -F nginx
```

### df - Display Disk Space Usage

The `df` command displays disk space usage of file systems.

#### Basic Syntax

```bash
df [OPTIONS] [FILESYSTEM...]
```

#### Common Options

- `-h`: Human-readable format
- `-i`: Inode information

#### DevOps Examples

```bash
# Check disk space usage
df -h

# Check inodes usage
df -i

# In monitoring scripts, alert on low disk space
if [ $(df / | tail -1 | awk '{print $5}' | sed 's/%//') -gt 90 ]; then
  echo "Warning: Disk space over 90%"
fi

# Check specific filesystem
df -h /var/lib/docker
```

### du - Display Disk Usage

The `du` command displays disk usage of files and directories.

#### Basic Syntax

```bash
du [OPTIONS] [FILE/DIRECTORY...]
```

#### Common Options

- `-h`: Human-readable format
- `-s`: Summary only
- `-a`: All files
- `--max-depth=N`: Limit directory depth

#### DevOps Examples

```bash
# Check size of directory
du -sh /var/log

# Find largest directories
du -h --max-depth=1 /var | sort -hr

# In cleanup scripts, find large log files
du -ah /var/log | sort -hr | head -10

# Check size of specific file types
du -ah /opt/myapp | grep "\.log$"
```

### free - Display Memory Usage

The `free` command displays system memory usage.

#### Basic Syntax

```bash
free [OPTIONS]
```

#### Common Options

- `-h`: Human-readable format
- `-m`: Display in megabytes
- `-s SECONDS`: Continuous display

#### DevOps Examples

```bash
# Check memory usage
free -h

# Monitor memory usage continuously
free -s 5

# In monitoring scripts, check available memory
available_memory=$(free -m | awk 'NR==2{printf "%.2f", $7*100/$2 }')
echo "Available memory: $available_memory%"
```

## Network Commands

### curl - Transfer Data from or to a Server

The `curl` command transfers data using various protocols.

#### Basic Syntax

```bash
curl [OPTIONS] [URL]
```

#### Common Options

- `-X`: Specify request method
- `-H`: Add header
- `-d`: Send data
- `-o`: Output to file
- `-I`: Fetch headers only

#### DevOps Examples

```bash
# Check if web service is running
curl -I http://localhost:8080/health

# Send JSON data to API
curl -X POST -H "Content-Type: application/json" -d '{"key":"value"}' http://api.example.com/data

# Download file
curl -o file.zip http://example.com/file.zip

# In CI/CD, test API endpoints
curl -f http://localhost:8080/api/status || exit 1

# Check response time
curl -w "@curl-format.txt" -o /dev/null -s http://example.com
```

### wget - Download Files from Web

The `wget` command downloads files from the web.

#### Basic Syntax

```bash
wget [OPTIONS] [URL]
```

#### DevOps Examples

```bash
# Download file
wget http://example.com/file.tar.gz

# Download file with specific name
wget -O newname.zip http://example.com/file.zip

# Download recursively
wget -r http://example.com/docs/

# In automation scripts, download dependencies
wget https://github.com/user/repo/releases/latest/download/app.tar.gz
```

### netstat - Network Statistics

The `netstat` command displays network connections and statistics.

#### Basic Syntax

```bash
netstat [OPTIONS]
```

#### Common Options

- `-t`: TCP connections
- `-u`: UDP connections
- `-l`: Listening ports
- `-p`: Process information
- `-n`: Numeric output

#### DevOps Examples

```bash
# List listening ports
netstat -tlnp

# Check connections to specific port
netstat -an | grep :8080

# In troubleshooting, find process using port
netstat -tlnp | grep :80

# Display network interface statistics
netstat -i
```

### ss - Socket Statistics

The `ss` command is a faster alternative to `netstat`.

#### Basic Syntax

```bash
ss [OPTIONS]
```

#### DevOps Examples

```bash
# List all TCP connections
ss -t

# List listening sockets
ss -l

# Show process using socket
ss -p

# Filter by state
ss -t state established

# In container environments, check network connections
ss -tuln
```

## Process Management Commands

### kill - Terminate Processes

The `kill` command sends signals to processes.

#### Basic Syntax

```bash
kill [OPTIONS] PID...
```

#### Common Signals

- `TERM` (15): Request graceful termination
- `KILL` (9): Force immediate termination
- `HUP` (1): Hang up

#### DevOps Examples

```bash
# Gracefully terminate process
kill 1234

# Force kill process
kill -9 1234

# Reload configuration
kill -HUP 1234

# In automation scripts, stop service gracefully
kill -TERM $(pgrep myapp)
```

### killall - Kill Processes by Name

The `killall` command kills processes by name.

#### Basic Syntax

```bash
killall [OPTIONS] NAME...
```

#### DevOps Examples

```bash
# Kill all processes with specific name
killall nginx

# Kill with specific signal
killall -9 apache2

# In deployment scripts, restart service
killall myapp && /opt/myapp/start.sh
```

### jobs - Display Jobs

The `jobs` command displays status of jobs.

#### Basic Syntax

```bash
jobs [OPTIONS]
```

#### DevOps Examples

```bash
# List background jobs
jobs

# List jobs with process IDs
jobs -l
```

### nohup - Run Command Immune to Hangups

The `nohup` command runs commands immune to hangups.

#### Basic Syntax

```bash
nohup COMMAND [ARGS] &
```

#### DevOps Examples

```bash
# Run long-running process in background
nohup ./long-running-script.sh &

# Start service that survives terminal disconnect
nohup java -jar myapp.jar > app.log 2>&1 &

# In deployment, start application
nohup /opt/myapp/start.sh > /var/log/myapp.log 2>&1 &
```

## Archive and Compression Commands

### tar - Tape Archive

The `tar` command creates and extracts tar archives.

#### Basic Syntax

```bash
tar [OPTIONS] [FILE...]
```

#### Common Options

- `-c`: Create archive
- `-x`: Extract archive
- `-f`: Specify filename
- `-z`: Compress with gzip
- `-j`: Compress with bzip2
- `-v`: Verbose output

#### DevOps Examples

```bash
# Create compressed archive
tar -czf backup.tar.gz /home/<USER>/data

# Extract archive
tar -xzf backup.tar.gz

# In CI/CD, package application
tar -czf app-release.tar.gz src/ config/ scripts/

# List contents of archive
tar -tzf backup.tar.gz

# Extract specific files
tar -xzf backup.tar.gz path/to/file
```

### gzip - Compress Files

The `gzip` command compresses files using Lempel-Ziv coding.

#### Basic Syntax

```bash
gzip [OPTIONS] [FILE...]
```

#### DevOps Examples

```bash
# Compress file
gzip largefile.log

# Compress multiple files
gzip *.log

# Decompress file
gunzip largefile.log.gz

# In log rotation, compress old logs
gzip /var/log/*.old
```

### zip - Package and Compress Files

The `zip` command packages and compresses files.

#### Basic Syntax

```bash
zip [OPTIONS] ARCHIVE FILE...
```

#### DevOps Examples

```bash
# Create zip archive
zip -r project.zip src/ docs/

# In cross-platform environments, create Windows-compatible archives
zip -r application.zip app/

# Update existing archive
zip app.zip newfile.txt
```

## Permission Management Commands

### chmod - Change File Permissions

The `chmod` command changes file permissions.

#### Basic Syntax

```bash
chmod [OPTIONS] PERMISSIONS FILE...
```

#### Permission Formats

- Numeric: 755, 644, 600
- Symbolic: u+r, g-w, o=r

#### DevOps Examples

```bash
# Set executable permissions
chmod +x script.sh

# Set specific permissions
chmod 644 config.properties

# Set permissions recursively
chmod -R 755 /opt/myapp/scripts/

# Remove write permissions
chmod a-w sensitive-file.txt

# In deployment, set secure permissions
chmod 600 /etc/myapp/private.key
```

### chown - Change File Owner

The `chown` command changes file ownership.

#### Basic Syntax

```bash
chown [OPTIONS] OWNER[:GROUP] FILE...
```

#### DevOps Examples

```bash
# Change file owner
chown user1 file.txt

# Change owner and group
chown user1:group1 file.txt

# Change ownership recursively
chown -R www-data:www-data /var/www/html

# In container environments, fix ownership
chown -R 1000:1000 /app/data
```

### chgrp - Change Group Ownership

The `chgrp` command changes group ownership.

#### Basic Syntax

```bash
chgrp [OPTIONS] GROUP FILE...
```

#### DevOps Examples

```bash
# Change group ownership
chgrp developers project/

# Change group recursively
chgrp -R webadmins /var/www/
```

## Conclusion

Mastering these essential Linux commands is crucial for DevOps practitioners. They form the foundation for system administration, automation, monitoring, and troubleshooting tasks. Practice these commands in real-world scenarios to build proficiency and efficiency in your DevOps workflows.

Regular use of these commands in:
- System administration tasks
- CI/CD pipelines
- Container management
- Log analysis and monitoring
- File system operations
- Process management
- Network troubleshooting

...will significantly improve your effectiveness as a DevOps engineer.