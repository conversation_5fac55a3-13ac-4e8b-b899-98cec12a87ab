# Classless Inter-Domain Routing (CIDR)

Classless Inter-Domain Routing (CIDR) is a method for allocating IP addresses and routing Internet Protocol packets. Introduced in 1993, CIDR replaced the previous classful network design and significantly improved how IP address space is allocated and routed.

## What is CIDR?

CIDR is a notation and methodology that allows for more flexible and efficient allocation of IP addresses compared to the older classful addressing system. It enables network administrators to define network prefixes of arbitrary length, rather than being restricted to the fixed boundaries of Class A, B, or C networks.

### How CIDR Differs from Classful Addressing

#### Classful Addressing

Before CIDR, IP addresses were divided into fixed classes:

- **Class A**: 0.0.0.0 to *************** (/8)
  - Network portion: First octet
  - Host portion: Last three octets
  - Supports up to 16 million hosts

- **Class B**: ********* to *************** (/16)
  - Network portion: First two octets
  - Host portion: Last two octets
  - Supports up to 65,000 hosts

- **Class C**: ********* to *************** (/24)
  - Network portion: First three octets
  - Host portion: Last octet
  - Supports up to 254 hosts

#### Problems with Classful Addressing

1. **Inefficient Address Allocation**: Organizations often received more IP addresses than they needed, leading to waste.
2. **Rapid Growth of Routing Tables**: Each classful network required a separate entry in routing tables.
3. **Lack of Flexibility**: Organizations couldn't get address blocks that matched their actual needs.

#### CIDR Advantages

1. **Efficient Address Allocation**: Organizations can receive exactly the number of IP addresses they need.
2. **Reduced Routing Table Size**: CIDR allows route aggregation (supernetting), reducing the number of entries in routing tables.
3. **Flexible Network Design**: Networks can be sized precisely to meet requirements.
4. **Hierarchical Routing**: Enables better organization of IP address space.

## CIDR Notation

CIDR notation is a compact representation of an IP address and its associated routing prefix. It consists of an IP address followed by a slash and a decimal number representing the number of significant bits in the network prefix.

### Format

```
IP_Address/Prefix_Length
```

For example:
- ***********/24
- 10.0.0.0/16
- **********/12

### Understanding Prefix Length

The prefix length (the number after the slash) indicates how many bits of the IP address represent the network portion:

- /24 means the first 24 bits (3 octets) are the network portion
- /16 means the first 16 bits (2 octets) are the network portion
- /8 means the first 8 bits (1 octet) are the network portion

## Calculating Network Ranges with CIDR

### Converting CIDR to Subnet Mask

To convert CIDR notation to a subnet mask:

1. Write the prefix length as a series of 1s
2. Fill the remaining bits with 0s to make 32 bits total
3. Convert each 8-bit group to decimal

#### Example: /26

1. 26 bits of 1s: ********.********.********.********
2. Convert to decimal: ***************

### Calculating Network Address

The network address is the first address in a CIDR block and is used to identify the network.

#### Method:
1. Convert the IP address to binary
2. Apply the subnet mask (set host bits to 0)
3. Convert back to decimal

#### Example: ***********00/26

1. IP in binary: ********.10101000.00000001.01100100
2. Mask /26:     ********.********.********.********
3. Network:      ********.10101000.00000001.01000000
4. Network address: ************

### Calculating Broadcast Address

The broadcast address is the last address in a CIDR block and is used to send messages to all hosts in the network.

#### Method:
1. Convert the IP address to binary
2. Apply the subnet mask (set host bits to 1)
3. Convert back to decimal

#### Example: ***********00/26

1. IP in binary: ********.10101000.00000001.01100100
2. Inverse mask: 00000000.00000000.00000000.00111111
3. Broadcast:    ********.10101000.00000001.01111111
4. Broadcast address: ***********27

### Calculating Usable Host Range

The usable host range excludes the network address and broadcast address.

#### Example: ************/26

- Network address: ************
- Broadcast address: ***********27
- Usable range: ************ to ***********26
- Number of hosts: 62 (2^(32-26) - 2 = 64 - 2 = 62)

## CIDR Block Sizes

Common CIDR blocks and their characteristics:

| CIDR | Subnet Mask     | Hosts | Block Size |
|------|-----------------|-------|------------|
| /30  | *************** | 2     | 4          |
| /29  | *************** | 6     | 8          |
| /28  | *************** | 14    | 16         |
| /27  | *************** | 30    | 32         |
| /26  | *************** | 62    | 64         |
| /25  | *************** | 126   | 128        |
| /24  | *************   | 254   | 256        |
| /23  | *************   | 510   | 512        |
| /22  | *************   | 1022  | 1024       |
| /21  | *************   | 2046  | 2048       |
| /20  | *************   | 4094  | 4096       |
| /16  | ***********     | 65534 | 65536      |

## Practical Examples of CIDR Usage

### Example 1: Allocating IP Addresses for a Small Office

A small office needs IP addresses for 20 devices. Using CIDR, we can allocate exactly what's needed:

- Requirement: 20 hosts
- Closest block size: 32 addresses (/27)
- CIDR notation: ***********/27
- Subnet mask: ***************
- Network address: ***********
- Broadcast address: ************
- Usable range: *********** to ************
- Number of hosts: 30

This allocation provides 10 extra addresses for future growth while avoiding waste.

### Example 2: Route Aggregation (Supernetting)

A company has four Class C networks:
- ***********/24
- ***********/24
- ***********/24
- ***********/24

Instead of advertising four separate routes, CIDR allows aggregation:
- Aggregated CIDR: ***********/22
- This single route covers all four networks
- Reduces routing table size from 4 entries to 1

### Example 3: Variable Length Subnet Masking (VLSM)

An organization has the network **********/16 and needs to create subnets for:
- Engineering department: 1000 hosts
- Sales department: 500 hosts
- HR department: 50 hosts
- IT department: 20 hosts

Using VLSM with CIDR:

1. **Engineering** (1000 hosts):
   - Need 2^10 = 1024 addresses (/22)
   - Subnet: **********/22
   - Range: ********** to ************

2. **Sales** (500 hosts):
   - Need 2^9 = 512 addresses (/23)
   - Subnet: **********/23
   - Range: ********** to ************

3. **HR** (50 hosts):
   - Need 2^6 = 64 addresses (/26)
   - Subnet: **********/26
   - Range: ********** to ***********

4. **IT** (20 hosts):
   - Need 2^5 = 32 addresses (/27)
   - Subnet: ***********/27
   - Range: *********** to ***********

## CIDR and IPv6

CIDR notation is also used with IPv6 addresses, though the principles are the same:

- IPv6 CIDR example: 2001:db8::/32
- The number after the slash represents the prefix length in bits
- IPv6 addresses are 128 bits, so prefix lengths range from 0 to 128

## Benefits of CIDR

1. **Efficient Address Allocation**: Organizations receive only the number of addresses they need
2. **Reduced Routing Table Size**: Route aggregation reduces the number of entries in routing tables
3. **Scalability**: Enables the Internet to grow without exhausting routing resources
4. **Flexibility**: Allows for precise network sizing
5. **Hierarchical Addressing**: Supports better organization of IP address space

## CIDR Best Practices

1. **Plan Network Hierarchy**: Design a logical hierarchy for your network addressing
2. **Use Route Aggregation**: Combine multiple routes into a single aggregated route when possible
3. **Document CIDR Allocations**: Keep detailed records of CIDR block assignments
4. **Reserve Address Space**: Set aside address blocks for future expansion
5. **Implement VLSM**: Use Variable Length Subnet Masking for efficient address allocation
6. **Monitor Address Usage**: Track how CIDR blocks are being utilized

## Common CIDR Mistakes to Avoid

1. **Inadequate Planning**: Not considering future growth when allocating CIDR blocks
2. **Wasting Address Space**: Allocating larger blocks than necessary
3. **Poor Documentation**: Failing to document CIDR assignments
4. **Ignoring Route Aggregation**: Not taking advantage of CIDR's route aggregation benefits
5. **Incorrect Calculations**: Making errors in CIDR calculations
6. **Overlapping Allocations**: Assigning overlapping CIDR blocks

## Conclusion

CIDR revolutionized IP address allocation and routing by providing a more flexible and efficient alternative to classful addressing. By allowing variable-length subnet masks and route aggregation, CIDR has helped extend the life of IPv4 address space and improved the scalability of the Internet. Understanding CIDR notation, calculation methods, and best practices is essential for network administrators working with modern IP networks. As networks continue to grow and evolve, CIDR remains a fundamental concept that enables efficient and scalable network design.