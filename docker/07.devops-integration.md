# Docker DevOps Integration

Integrating Docker into DevOps practices enables streamlined CI/CD pipelines, automated testing, efficient deployment strategies, and effective logging and debugging. This guide covers essential Docker practices for modern DevOps workflows.

## CI/CD Pipelines with Docker

Docker integrates seamlessly into CI/CD pipelines, providing consistent environments throughout the development lifecycle.

### Basic CI/CD Pipeline

```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

before_script:
  - docker info

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA

test:
  stage: test
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker run $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA npm test

deploy:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:latest
  only:
    - main
```

### Multi-Stage CI/CD Pipeline

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1
    
    - name: Login to DockerHub
      uses: docker/login-action@v1
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    
    - name: Build and push
      uses: docker/build-push-action@v2
      with:
        context: .
        push: true
        tags: myapp:${{ github.sha }},myapp:latest

  test:
    runs-on: ubuntu-latest
    needs: build
    steps:
    - name: Run tests
      run: |
        docker run myapp:${{ github.sha }} npm test

  deploy-staging:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Deploy to staging
      run: |
        ssh user@staging-server \
          "docker pull myapp:${{ github.sha }} && \
           docker stop myapp-staging 2>/dev/null || true && \
           docker rm myapp-staging 2>/dev/null || true && \
           docker run -d --name myapp-staging -p 3000:3000 myapp:${{ github.sha }}"

  deploy-production:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - name: Deploy to production
      run: |
        ssh user@prod-server \
          "docker pull myapp:${{ github.sha }} && \
           docker stop myapp-prod 2>/dev/null || true && \
           docker rm myapp-prod 2>/dev/null || true && \
           docker run -d --name myapp-prod -p 80:3000 myapp:${{ github.sha }}"
```

### Docker in Jenkins Pipeline

```groovy
// Jenkinsfile
pipeline {
  agent any
  
  environment {
    DOCKER_IMAGE = "myapp"
    DOCKER_TAG = "${env.BUILD_NUMBER}"
  }
  
  stages {
    stage('Build') {
      steps {
        script {
          docker.build("${DOCKER_IMAGE}:${DOCKER_TAG}")
        }
      }
    }
    
    stage('Test') {
      steps {
        script {
          docker.image("${DOCKER_IMAGE}:${DOCKER_TAG}").inside {
            sh 'npm test'
          }
        }
      }
    }
    
    stage('Push') {
      steps {
        script {
          docker.withRegistry('https://registry.hub.docker.com', 'docker-hub-credentials') {
            docker.image("${DOCKER_IMAGE}:${DOCKER_TAG}").push()
            docker.image("${DOCKER_IMAGE}:${DOCKER_TAG}").push('latest')
          }
        }
      }
    }
    
    stage('Deploy') {
      steps {
        sh '''
          ssh user@server \
            "docker pull ${DOCKER_IMAGE}:${DOCKER_TAG} && \
             docker stop myapp 2>/dev/null || true && \
             docker rm myapp 2>/dev/null || true && \
             docker run -d --name myapp -p 80:3000 ${DOCKER_IMAGE}:${DOCKER_TAG}"
        '''
      }
    }
  }
}
```

## Automated Testing in Containers

Running tests in containers ensures consistency and reliability across environments.

### Unit Testing

```dockerfile
# Dockerfile.test
FROM node:18

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .

CMD ["npm", "test"]
```

```bash
# Run unit tests in container
docker build -f Dockerfile.test -t myapp-test .
docker run myapp-test

# Run with coverage
docker run myapp-test npm run test:coverage

# Run specific test suite
docker run myapp-test npm run test:unit
```

### Integration Testing

```yaml
# docker-compose.test.yml
version: "3.8"
services:
  sut:  # System Under Test
    build: .
    depends_on:
      - db
      - cache
    environment:
      - DATABASE_URL=******************************/test
      - REDIS_URL=redis://cache:6379
    volumes:
      - ./test-results:/app/test-results
  
  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=test
      - POSTGRES_USER=test
      - POSTGRES_PASSWORD=test
    
  cache:
    image: redis:alpine
```

```bash
# Run integration tests
docker-compose -f docker-compose.test.yml up -d

docker-compose -f docker-compose.test.yml run sut npm run test:integration

# Capture test results
docker-compose -f docker-compose.test.yml run sut npm run test:integration -- --reporters=junit --outputFile=test-results/results.xml

docker-compose -f docker-compose.test.yml down
```

### Contract Testing

```bash
# Run contract tests with Pact
docker run \
  -v $(pwd):/app \
  -w /app \
  pactfoundation/pact-cli:latest \
  broker publish \
  /app/pacts \
  --broker-base-url http://pact-broker:9292 \
  --consumer-app-version ${GIT_COMMIT}
```

### Security Testing

```bash
# Run security scans in pipeline
#!/bin/bash

docker build -t myapp:$COMMIT_SHA .

# Scan for vulnerabilities
docker run --rm \
  -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy:latest \
  image myapp:$COMMIT_SHA

# Scan for misconfigurations
docker run --rm \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v $(pwd):/app \
  aquasec/trivy:latest \
  config --exit-code 1 /app

# Run OWASP ZAP scan
docker run --rm \
  -t owasp/zap2docker-stable \
  zap-baseline.py -t http://myapp:3000
```

## Deployment Strategies

Docker enables various deployment strategies for reliable application updates.

### Blue-Green Deployment

```bash
# Blue-Green deployment script
#!/bin/bash

NEW_VERSION=$1
ACTIVE_COLOR=$(curl -s http://loadbalancer/color)

if [ "$ACTIVE_COLOR" = "blue" ]; then
  DEPLOY_COLOR="green"
  DEPLOY_PORT=8081
else
  DEPLOY_COLOR="blue"
  DEPLOY_PORT=8080
fi

# Deploy new version
docker run -d \
  --name myapp-$DEPLOY_COLOR \
  -p $DEPLOY_PORT:3000 \
  myapp:$NEW_VERSION

# Health check
HEALTH_CHECK="http://localhost:$DEPLOY_PORT/health"
for i in {1..30}; do
  if curl -f $HEALTH_CHECK; then
    echo "Health check passed"
    break
  fi
  echo "Waiting for health check..."
  sleep 10
done

# Switch traffic
echo "Switching traffic to $DEPLOY_COLOR"
curl -X POST http://loadbalancer/switch?color=$DEPLOY_COLOR

# Stop old version
if [ "$ACTIVE_COLOR" = "blue" ]; then
  docker stop myapp-blue 2>/dev/null || true
  docker rm myapp-blue 2>/dev/null || true
else
  docker stop myapp-green 2>/dev/null || true
  docker rm myapp-green 2>/dev/null || true
fi
```

### Rolling Updates

```bash
# Rolling update with Docker Swarm
docker service create \
  --name web \
  --replicas 5 \
  --update-delay 10s \
  --update-parallelism 1 \
  --rollback-parallelism 1 \
  nginx:alpine

# Update service
docker service update \
  --image nginx:latest \
  web

# In docker-compose.yml
version: "3.8"
services:
  web:
    image: myapp:latest
    deploy:
      replicas: 5
      update_config:
        parallelism: 2
        delay: 10s
        order: start-first
        failure_action: rollback
      rollback_config:
        parallelism: 2
        delay: 10s
        order: stop-first
```

### Canary Deployment

```bash
# Canary deployment script
#!/bin/bash

NEW_VERSION=$1
CANARY_COUNT=$2

# Deploy canary instances
docker run -d --name myapp-canary-$i -p 300$((10+$i)):3000 myapp:$NEW_VERSION

# Health check canaries
for i in $(seq 1 $CANARY_COUNT); do
  curl -f http://localhost:300$((10+$i))/health || exit 1
done

# Monitor canaries for 30 minutes
for minute in {1..30}; do
  ERROR_COUNT=0
  for i in $(seq 1 $CANARY_COUNT); do
    if ! curl -f http://localhost:300$((10+$i))/health; then
      ERROR_COUNT=$((ERROR_COUNT + 1))
    fi
  done
  
  if [ $ERROR_COUNT -gt 0 ]; then
    echo "Errors detected, rolling back"
    for i in $(seq 1 $CANARY_COUNT); do
      docker stop myapp-canary-$i
      docker rm myapp-canary-$i
    done
    exit 1
  fi
  
  sleep 60
done

# Deploy to remaining instances
echo "Canary testing successful, deploying to all instances"
# Implementation depends on orchestration platform
```

## Logging and Debugging Containerized Applications

Effective logging and debugging are crucial for maintaining containerized applications.

### Centralized Logging

```yaml
# docker-compose.logging.yml
version: "3.8"
services:
  web:
    image: myapp
    logging:
      driver: "fluentd"
      options:
        fluentd-address: localhost:24224
        tag: myapp.web
  
  api:
    image: myapp-api
    logging:
      driver: "fluentd"
      options:
        fluentd-address: localhost:24224
        tag: myapp.api
  
  fluentd:
    image: fluent/fluentd:v1.12-1
    volumes:
      - ./fluentd/conf:/fluentd/etc
      - ./logs:/var/log
    ports:
      - "24224:24224"
```

### Structured Logging

```javascript
// Node.js structured logging example
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  defaultMeta: { service: 'myapp' },
  transports: [
    new winston.transports.Console()
  ]
});

// Log with context
logger.info('User login', { 
  userId: 123, 
  ip: '***********', 
  userAgent: 'Mozilla/5.0' 
});

// Error logging with stack trace
try {
  // Some operation
} catch (error) {
  logger.error('Operation failed', { 
    error: error.message, 
    stack: error.stack,
    context: 'user-registration'
  });
}
```

### Debugging Running Containers

```bash
# Inspect container configuration
docker inspect myapp

docker inspect --format='{{.State.Running}}' myapp

docker inspect --format='{{.NetworkSettings.IPAddress}}' myapp

# Monitor container resources
docker stats myapp

docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" myapp

# Check container logs
docker logs myapp

docker logs --since 1h myapp

docker logs -f myapp  # Follow logs

# Execute commands in running container
docker exec -it myapp /bin/bash

docker exec myapp ps aux

docker exec myapp netstat -tuln

# Copy files for debugging
docker cp myapp:/app/logs ./debug-logs

# Debug with nsenter (access container namespaces)
PID=$(docker inspect -f '{{.State.Pid}}' myapp)
sudo nsenter -t $PID -n netstat -tuln
```

### Health Monitoring

```bash
# Create monitoring script
#!/bin/bash
# monitor.sh

CONTAINERS=$(docker ps --format "{{.Names}}")

for container in $CONTAINERS; do
  # Check if container is running
  if ! docker ps --format "{{.Names}}" | grep -q $container; then
    echo "CRITICAL: $container is not running"
    # Send alert
    continue
  fi
  
  # Check health status
  HEALTH_STATUS=$(docker inspect --format='{{if .State.Health}}{{.State.Health.Status}}{{else}}healthy{{end}}' $container)
  if [ "$HEALTH_STATUS" != "healthy" ]; then
    echo "WARNING: $container health status is $HEALTH_STATUS"
    # Send alert
  fi
  
  # Check resource usage
  CPU_USAGE=$(docker stats --no-stream --format "{{.CPUPerc}}" $container | sed 's/%//')
  if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "WARNING: $container CPU usage is ${CPU_USAGE}%"
  fi
  
  MEM_USAGE=$(docker stats --no-stream --format "{{.MemPerc}}" $container | sed 's/%//')
  if (( $(echo "$MEM_USAGE > 80" | bc -l) )); then
    echo "WARNING: $container memory usage is ${MEM_USAGE}%"
  fi
done
```

### Debugging Network Issues

```bash
# Create network debugging container
docker run -it --network container:myapp nicolaka/netshoot

# Test connectivity from within container
docker exec myapp curl -v http://api:3000/health

docker exec myapp nslookup database

docker exec myapp ping database

# Check network configuration
docker exec myapp ip route

docker exec myapp cat /etc/resolv.conf

# Monitor network traffic
docker run --network container:myapp tcpdump -i eth0
```

This comprehensive guide to Docker DevOps integration provides the knowledge needed to implement robust CI/CD pipelines, automated testing, deployment strategies, and effective logging and debugging practices for containerized applications.
