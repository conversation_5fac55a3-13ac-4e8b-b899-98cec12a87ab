# Docker Core Commands and Operations

Mastering Docker commands is essential for effective container management in DevOps workflows. This guide covers the fundamental commands for image management, container lifecycle, interactive operations, and data persistence.

## Image Management

Docker images are the foundation of containers. Managing images effectively is crucial for maintaining a clean and efficient development environment.

### docker pull

Downloads images from a registry (default is Docker Hub).

```bash
# Pull the latest nginx image
docker pull nginx

# Pull a specific version
docker pull nginx:1.21

# Pull from a private registry
docker pull myregistry.com/myapp:latest

# Pull all tags of an image
docker pull -a ubuntu
```

In CI/CD pipelines, you might pull images before building:
```bash
#!/bin/bash
# Pull base image before building
docker pull python:3.9-slim

docker build -t myapp .
```

### docker build

Creates an image from a Dockerfile.

```bash
# Build with default Dockerfile in current directory
docker build -t myapp .

# Build with specific Dockerfile
docker build -f Dockerfile.prod -t myapp:prod .

# Build with build arguments
docker build --build-arg NODE_ENV=production -t myapp .

# Build without cache
docker build --no-cache -t myapp .
```

In automated environments:
```bash
# Build and tag with git commit hash
docker build -t myapp:$(git rev-parse --short HEAD) .
```

### docker images

Lists available images on the system.

```bash
# List all images
docker images

# List images with a specific name
docker images nginx

# List images with digests
docker images --digests

# List images in quiet mode (IDs only)
docker images -q

# Filter images
docker images --filter "before=nginx:latest"
docker images --filter "label=version=1.0"
```

For cleanup scripts:
```bash
# Remove dangling images
docker image prune -f

# Remove unused images
docker image prune -a -f
```

### docker rmi

Removes images from the system.

```bash
# Remove an image by name
docker rmi nginx

# Remove multiple images
docker rmi nginx ubuntu

# Force removal (if containers are using the image)
docker rmi -f myapp

# Remove by image ID
docker rmi 1234567890ab
```

For automated cleanup:
```bash
# Remove images older than 30 days
OLD_IMAGES=$(docker images --format "{{.ID}}\t{{.CreatedSince}}" | awk '$2 ~ /weeks|months|years/ {print $1}')
if [ -n "$OLD_IMAGES" ]; then
  docker rmi $OLD_IMAGES
fi
```

## Container Lifecycle

Managing the container lifecycle is a core DevOps skill for deploying and maintaining applications.

### docker run

Creates and starts a new container from an image.

```bash
# Run a container in foreground
docker run nginx

# Run a container in background (-d)
docker run -d nginx

# Run with a custom name
docker run --name webserver nginx

# Run with port mapping
docker run -d -p 8080:80 nginx

# Run with environment variables
docker run -d -e NODE_ENV=production myapp

# Run with resource limits
docker run -d --memory=512m --cpus=1.5 nginx

# Run with restart policy
docker run -d --restart=unless-stopped nginx
```

In development environments:
```bash
# Run with volume for live code updates
docker run -d -p 3000:3000 -v $(pwd):/app myapp
```

### docker start/stop

Controls running containers.

```bash
# Stop a running container
docker stop webserver

# Start a stopped container
docker start webserver

# Restart a container
docker restart webserver

# Stop multiple containers
docker stop webserver dbserver

# Stop with a custom timeout (default is 10 seconds)
docker stop -t 30 webserver
```

For maintenance scripts:
```bash
# Gracefully stop all containers
docker stop $(docker ps -q)
```

### docker ps

Lists running containers.

```bash
# List running containers
docker ps

# List all containers (running and stopped)
docker ps -a

# List only container IDs
docker ps -q

# List with size information
docker ps -s

# Filter containers
docker ps --filter "status=running"
docker ps --filter "ancestor=nginx"
```

For monitoring:
```bash
# Check if a specific container is running
if docker ps --format "{{.Names}}" | grep -q "webserver"; then
  echo "Webserver is running"
else
  echo "Webserver is not running"
fi
```

### docker rm

Removes containers from the system.

```bash
# Remove a stopped container
docker rm webserver

# Remove multiple containers
docker rm webserver dbserver

# Force removal (if container is running)
docker rm -f webserver

# Remove all stopped containers
docker rm $(docker ps -aq -f status=exited)
```

For cleanup automation:
```bash
# Remove containers older than 24 hours
OLD_CONTAINERS=$(docker ps -a --format "{{.ID}}\t{{.CreatedAt}}" | awk '$2 < "$(date -d '1 day ago' +%s)" {print $1}')
if [ -n "$OLD_CONTAINERS" ]; then
  docker rm $OLD_CONTAINERS
fi
```

## Interactive Containers

Working with containers interactively is essential for debugging and development.

### docker exec

Runs commands in a running container.

```bash
# Execute a command in a container
docker exec webserver ls /etc

# Execute interactively (-i) with a terminal (-t)
docker exec -it webserver /bin/bash

# Execute as a different user
docker exec -it --user root webserver /bin/bash

# Execute with environment variables
docker exec -it -e DEBUG=true webserver printenv
```

For troubleshooting:
```bash
# Check application logs inside container
docker exec webserver tail -f /var/log/app.log

# Install debugging tools in container
docker exec webserver apt-get update && apt-get install -y curl
```

### docker attach

Attaches to a running container's process.

```bash
# Attach to container's main process
docker attach webserver

# Detach using Ctrl+P Ctrl+Q (don't use Ctrl+C)
```

Note: `docker attach` connects to the main process, while `docker exec` starts a new process.

## Data Persistence

Managing data in containers is critical for stateful applications.

### Volumes

Docker volumes are the preferred mechanism for persisting data.

```bash
# Create a named volume
docker volume create myvolume

# Run container with a named volume
docker run -d -v myvolume:/data nginx

# List volumes
docker volume ls

# Inspect a volume
docker volume inspect myvolume

# Remove unused volumes
docker volume prune
```

### Bind Mounts

Bind mounts link container paths to host filesystem paths.

```bash
# Run with bind mount
docker run -d -v /host/path:/container/path nginx

# Run with read-only bind mount
docker run -d -v /host/path:/container/path:ro nginx

# In development, mount current directory
docker run -d -v $(pwd):/app -w /app python:3.9 python app.py
```

### tmpfs Mounts

tmpfs mounts store data in host memory (Linux only).

```bash
# Run with tmpfs mount
docker run -d --tmpfs /tmp:rw,noexec,nosuid,size=100m nginx
```

For database containers:
```bash
# Run PostgreSQL with persistent volume
docker run -d \
  --name postgres-db \
  -e POSTGRES_PASSWORD=mypassword \
  -v postgres-data:/var/lib/postgresql/data \
  -p 5432:5432 \
  postgres:13
```

This comprehensive guide to Docker core commands provides the foundation for effective container management in DevOps environments.
