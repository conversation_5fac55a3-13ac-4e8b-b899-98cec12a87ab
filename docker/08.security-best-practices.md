# Docker Security Best Practices

Security is a critical aspect of containerized applications in production environments. This guide covers essential security practices for Docker, including image security, runtime security, network security, and compliance considerations.

## Image Security

Securing Docker images is the foundation of container security. Vulnerabilities in base images can compromise entire containerized applications.

### Using Minimal Base Images

Start with minimal base images to reduce the attack surface:

```dockerfile
# Avoid (large attack surface)
FROM ubuntu:20.04
RUN apt-get update && apt-get install -y python3 python3-pip

# Prefer (minimal attack surface)
FROM python:3.9-slim

# Or even better for static binaries
FROM gcr.io/distroless/python3-debian11

# Or scratch for maximum security
FROM scratch
COPY myapp /
CMD ["/myapp"]
```

### Image Scanning and Vulnerability Management

Regularly scan images for vulnerabilities:

```bash
# Using Docker Scout (built into Docker Desktop)
docker scout cves myapp:latest

# Using Trivy (open source)
trivy image myapp:latest

# Using Clair (open source)
clair-scanner myapp:latest

# In CI/CD pipeline
#!/bin/bash

docker build -t myapp:$COMMIT_SHA .

# Scan for critical and high severity vulnerabilities
trivy image --severity CRITICAL,HIGH myapp:$COMMIT_SHA
if [ $? -ne 0 ]; then
  echo "Critical vulnerabilities found, failing build"
  exit 1
fi

docker push myapp:$COMMIT_SHA
```

### Signing and Verifying Images

Use content trust to ensure image integrity:

```bash
# Enable Docker Content Trust
export DOCKER_CONTENT_TRUST=1

# Pull only signed images
docker pull myapp:latest  # Only pulls if signed

# Push with signing
docker push myapp:latest  # Automatically signs the image

# Using Notary for advanced signing
notary -s https://notary.docker.io -d ~/.docker/trust \
  addhash myapp:latest 10240 \
  --sha256 1234567890abcdef...
```

### Multi-Stage Builds for Security

Use multi-stage builds to separate build and runtime environments:

```dockerfile
# Multi-stage build with security considerations

# Build stage
FROM node:18 AS builder
WORKDIR /app
COPY package*.json ./
# Install all dependencies including dev dependencies
RUN npm ci
COPY . .
RUN npm run build

# Security scan stage
FROM aquasec/trivy:latest AS scanner
COPY --from=builder /app /app
RUN trivy filesystem --severity CRITICAL,HIGH /app

# Runtime stage
FROM node:18-alpine AS runtime

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Copy only production dependencies
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist

# Switch to non-root user
USER nextjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000
CMD ["node", "server.js"]
```

## Runtime Security

Securing containers at runtime is essential for protecting running applications.

### Running as Non-Root User

Always run containers as non-root users:

```dockerfile
# In Dockerfile
FROM node:18-alpine

# Create app user and group
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Set ownership of app files
COPY --chown=nextjs:nodejs . /app
WORKDIR /app

# Switch to non-root user
USER nextjs

CMD ["node", "server.js"]
```

At runtime:
```bash
# Explicitly run as non-root user
docker run -d --user 1001:1001 myapp

# Run with dropped capabilities
docker run -d \
  --cap-drop=ALL \
  --cap-add=NET_BIND_SERVICE \
  myapp

# Run with read-only root filesystem
docker run -d \
  --read-only \
  --tmpfs /tmp \
  --tmpfs /var/run \
  myapp
```

### Capability Management

Drop unnecessary Linux capabilities:

```bash
# Drop all capabilities and add only required ones
docker run -d \
  --cap-drop=ALL \
  --cap-add=NET_BIND_SERVICE \
  --cap-add=SYS_PTRACE \
  myapp

# Common capability drops for security
# --cap-drop=SETUID  # Prevent changing user ID
# --cap-drop=SETGID  # Prevent changing group ID
# --cap-drop=NET_RAW # Prevent raw packet access
# --cap-drop=CHOWN   # Prevent changing file ownership

# In docker-compose.yml
version: "3.8"
services:
  web:
    image: myapp
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
```

### Security Options

Use Docker security options for additional protection:

```bash
# Prevent privilege escalation
docker run -d \
  --security-opt=no-new-privileges \
  myapp

# Use SELinux labeling
docker run -d \
  --security-opt=label=type:container_runtime_t \
  myapp

# Use AppArmor profiles
docker run -d \
  --security-opt=apparmor:docker-default \
  myapp

# Read-only container filesystem
docker run -d \
  --read-only \
  --tmpfs /tmp \
  --tmpfs /var/run \
  myapp
```

## Network Security

Securing container networks prevents unauthorized access and lateral movement.

### Network Isolation

Use custom networks for better isolation:

```bash
# Create isolated networks
docker network create --driver bridge frontend-net
docker network create --driver bridge backend-net

# Run frontend services on isolated network
docker run -d --name web \
  --network frontend-net \
  nginx

# Run backend services on isolated network
docker run -d --name db \
  --network backend-net \
  postgres

# Only connect necessary services between networks
docker run -d --name api \
  --network frontend-net \
  --network backend-net \
  myapp
```

### Port Security

Minimize exposed ports and use network policies:

```bash
# Only expose necessary ports
docker run -d \
  -p 127.0.0.1:3000:3000 \
  myapp

# Use Docker Swarm with network policies
version: "3.8"
services:
  web:
    image: nginx
    ports:
      - target: 80
        published: 8080
        mode: host
    networks:
      - frontend
  
  db:
    image: postgres
    networks:
      - backend
    
networks:
  frontend:
    driver: overlay
    attachable: true
  backend:
    driver: overlay
    internal: true  # No external access
```

## Secrets Management

Proper secrets management prevents credential exposure:

### Docker Secrets (Swarm Mode)

```bash
# Create secrets
echo "mysecretpassword" | docker secret create db-password -

# Use secrets in services
docker service create \
  --name db \
  --secret db-password \
  -e POSTGRES_PASSWORD_FILE=/run/secrets/db-password \
  postgres:13

# In docker-compose.yml
version: "3.8"
services:
  db:
    image: postgres:13
    environment:
      - POSTGRES_PASSWORD_FILE=/run/secrets/db-password
    secrets:
      - db-password

secrets:
  db-password:
    file: ./secrets/db-password.txt
```

### Environment Variables Security

```bash
# Avoid hardcoding secrets in Dockerfiles
# DON'T DO THIS:
# ENV DB_PASSWORD=mypassword

# Use build arguments for build-time secrets
# syntax=docker/dockerfile:1
FROM node:18
RUN --mount=type=secret,id=npmrc \
  cp /run/secrets/npmrc ~/.npmrc && \
  npm ci && \
  rm ~/.npmrc

# Use environment files (not in version control)
docker run -d --env-file .env myapp

# Use external secret management
docker run -d \
  -e DB_PASSWORD=$(vault read -field=password secret/myapp) \
  myapp
```

## Compliance and Auditing

Implement compliance and auditing for regulatory requirements.

### Docker Bench for Security

```bash
# Run Docker Bench for Security
docker run --rm \
  --pid=host \
  --userns=host \
  --cap-add audit_control \
  -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
  -v /etc:/etc:ro \
  -v /usr/bin/containerd:/usr/bin/containerd:ro \
  -v /usr/bin/runc:/usr/bin/runc:ro \
  -v /usr/lib/systemd:/usr/lib/systemd:ro \
  -v /var/lib:/var/lib:ro \
  -v /var/run/docker.sock:/var/run/docker.sock:ro \
  --label docker_bench_security \
  docker/docker-bench-security
```

### Audit Logging

```bash
# Enable Docker daemon audit logging
# /etc/docker/daemon.json
{
  "log-driver": "syslog",
  "log-opts": {
    "syslog-address": "tcp://************:123",
    "tag": "docker"
  }
}

# Monitor Docker events
docker events \
  --filter type=container \
  --filter event=start \
  --filter event=stop

# Log container activity
docker events --since 1h --until 10m
```

### Image Signing and Verification

```bash
# Set up Notary for image signing
export DOCKER_CONTENT_TRUST=1
export DOCKER_CONTENT_TRUST_SERVER=https://notary.example.com

# Sign an image
docker push myregistry.example.com/myapp:latest

# Verify image signature
docker pull myregistry.example.com/myapp:latest

# Inspect trust data
notary -s https://notary.example.com -d ~/.docker/trust \
  list myregistry.example.com/myapp
```

## Container Runtime Security

Implement runtime security monitoring and protection.

### Falco for Runtime Threat Detection

```yaml
# falco.yaml
rules_file:
  - /etc/falco/falco_rules.yaml
  - /etc/falco/falco_rules.local.yaml

# Custom rule for container security
- rule: Unexpected outbound connection
  desc: Detect unexpected outbound connections from containers
  condition: >
    container.id != host and
    outbound and
    not fd.sport in (80, 443) and
    not proc.name in (wget, curl)
  output: >
    Unexpected outbound connection (command=%proc.cmdline connection=%fd.name)
  priority: WARNING
```

### Sysdig for Container Monitoring

```bash
# Run Sysdig Secure for container monitoring
docker run -d --name sysdig-agent \
  --privileged \
  --network host \
  --pid host \
  -e SECURE_API_TOKEN=your-token \
  -v /var/run/docker.sock:/host/var/run/docker.sock \
  -v /dev:/host/dev \
  -v /proc:/host/proc:ro \
  -v /boot:/host/boot:ro \
  -v /lib/modules:/host/lib/modules:ro \
  -v /usr:/host/usr:ro \
  sysdig/agent
```

This comprehensive guide to Docker security best practices provides DevOps engineers with the knowledge to secure containerized applications throughout the development lifecycle, from image creation to runtime protection.
