# Docker Networking and Communication

Understanding Docker networking is crucial for connecting containers, exposing services, and enabling communication in containerized applications. This guide covers Docker networks, port mapping, container linking, and DNS resolution.

## Docker Networks

Docker networks provide isolated communication channels between containers and between containers and the host system.

### Network Drivers

Docker offers several network drivers for different use cases:

#### Bridge Networks

The default network driver for containers. Bridge networks are isolated from the host and other networks.

```bash
# Create a custom bridge network
docker network create --driver bridge mybridge

# Run containers on the custom bridge
docker run -d --name web --network mybridge nginx
docker run -d --name db --network mybridge postgres

# List networks
docker network ls

# Inspect a network
docker network inspect mybridge
```

#### Host Networks

Removes network isolation between the container and the host.

```bash
# Run container with host networking
docker run -d --name web --network host nginx

# Note: Port mapping is not needed with host networking
# Container uses host's network stack directly
```

#### Overlay Networks

Used for multi-host networking in Docker Swarm mode.

```bash
# Create an overlay network (requires Swarm mode)
docker swarm init  # Initialize swarm if not already done
docker network create --driver overlay --attachable myoverlay

# Run services on overlay network
docker service create --name web --network myoverlay nginx
docker service create --name db --network myoverlay postgres
```

#### None Network

Completely isolates the container from any network.

```bash
# Run container with no network
docker run -d --name isolated --network none alpine
```

### Custom Network Creation

Creating custom networks provides better isolation and control:

```bash
# Create a bridge network with custom settings
docker network create \
  --driver bridge \
  --subnet=**********/16 \
  --ip-range=************/20 \
  --gateway=********** \
  mycustomnet

# Run containers with static IPs (only works with custom networks)
docker run -d --name web \
  --network mycustomnet \
  --ip **********0 \
  nginx

docker run -d --name db \
  --network mycustomnet \
  --ip *********** \
  postgres
```

### Network Management

```bash
# Connect a running container to a network
docker network connect mybridge web

# Disconnect a container from a network
docker network disconnect mybridge web

# Remove unused networks
docker network prune

# Remove a specific network
docker network rm mybridge
```

## Port Mapping

Port mapping allows external access to services running in containers.

### Basic Port Mapping

```bash
# Map container port 80 to host port 8080
docker run -d -p 8080:80 nginx

# Map multiple ports
docker run -d -p 8080:80 -p 8443:443 nginx

# Map to specific host IP
docker run -d -p 127.0.0.1:8080:80 nginx

# Map container port to random host port
docker run -d -P nginx  # Capital P maps all exposed ports
```

### Port Mapping in Production

```bash
# For load balancing, map to different host ports
docker run -d --name web1 -p 8081:80 nginx
docker run -d --name web2 -p 8082:80 nginx
docker run -d --name web3 -p 8083:80 nginx

# Use with reverse proxy
docker run -d --name proxy -p 80:80 \
  -v /etc/nginx/conf.d:/etc/nginx/conf.d \
  nginx
```

### Container Communication

Containers on the same network can communicate using container names:

```bash
# Create a network
docker network create appnet

# Run database container
docker run -d --name database \
  --network appnet \
  -e POSTGRES_PASSWORD=mypassword \
  postgres

# Run application container that connects to database
docker run -d --name webapp \
  --network appnet \
  -e DB_HOST=database \
  -e DB_PASSWORD=mypassword \
  myapp
```

In the application code, you would connect to the database using the container name:
```python
# Python example
import psycopg2
conn = psycopg2.connect(
  host="database",  # Container name resolves to IP
  database="postgres",
  user="postgres",
  password="mypassword"
)
```

## Container Linking

Container linking is a legacy feature that's been largely replaced by custom networks, but understanding it is still valuable.

### Legacy Linking

```bash
# Link containers (legacy approach)
docker run -d --name database \
  -e POSTGRES_PASSWORD=mypassword \
  postgres

docker run -d --name webapp \
  --link database:db \
  -e DB_HOST=db \
  myapp
```

Environment variables created by linking:
```bash
# Linking creates environment variables in the linked container
DB_PORT=tcp://**********:5432
DB_PORT_5432_TCP=tcp://**********:5432
DB_PORT_5432_TCP_ADDR=**********
DB_PORT_5432_TCP_PORT=5432
DB_PORT_5432_TCP_PROTO=tcp
DB_NAME=/webapp/db
DB_ENV_POSTGRES_PASSWORD=mypassword
```

### Modern Approach with Custom Networks

```bash
# Modern approach using custom networks
docker network create appnet

docker run -d --name database \
  --network appnet \
  -e POSTGRES_PASSWORD=mypassword \
  postgres

docker run -d --name webapp \
  --network appnet \
  -e DB_HOST=database \
  myapp
```

## DNS Resolution

Docker provides built-in DNS resolution for containers on the same network.

### Container Name Resolution

```bash
# Containers on the same network can resolve each other by name
docker network create appnet

docker run -d --name web --network appnet nginx
docker run -d --name api --network appnet node:alpine

docker run -it --network appnet alpine sh
# Inside the alpine container:
ping web  # Resolves to web container IP
ping api  # Resolves to api container IP
nslookup web  # Shows DNS resolution details
```

### Custom DNS Settings

```bash
# Set custom DNS servers
docker run -d --name web \
  --dns ******* \
  --dns ******* \
  nginx

# Set DNS search domains
docker run -d --name web \
  --dns-search example.com \
  nginx

# Set DNS options
docker run -d --name web \
  --dns-opt timeout:3 \
  nginx
```

### Service Discovery in Docker Swarm

```bash
# In Swarm mode, services are automatically discoverable
docker service create --name database \
  --network myoverlay \
  postgres

docker service create --name webapp \
  --network myoverlay \
  --env DB_HOST=database \
  myapp
```

### Network Troubleshooting

```bash
# Check container network configuration
docker exec web ip addr show

docker exec web cat /etc/hosts

docker exec web cat /etc/resolv.conf

# Test connectivity between containers
docker exec web ping database

docker exec web nslookup database

# Check network connectivity
docker exec web netstat -tuln
```

### Advanced Networking Scenarios

#### Multi-tier Application

```bash
# Create networks for different tiers
docker network create frontend
docker network create backend

# Run frontend services
docker run -d --name loadbalancer \
  --network frontend \
  -p 80:80 \
  haproxy

docker run -d --name web1 \
  --network frontend \
  --network-alias web \
  nginx

docker run -d --name web2 \
  --network frontend \
  --network-alias web \
  nginx

# Run backend services
docker run -d --name database \
  --network backend \
  postgres

docker run -d --name cache \
  --network backend \
  redis

# Connect frontend to backend
docker network connect backend web1
docker network connect backend web2
```

This comprehensive guide to Docker networking provides DevOps engineers with the knowledge to design, implement, and troubleshoot container communication in various deployment scenarios.
