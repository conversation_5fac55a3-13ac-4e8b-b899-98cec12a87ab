# Docker Fundamentals for DevOps Practitioners

Docker is a containerization platform that enables developers and DevOps engineers to build, ship, and run applications in containers. Containers are lightweight, portable, and self-sufficient packages that include everything needed to run an application: code, runtime, system tools, libraries, and settings.

## What is Docker

Docker is a set of platform-as-a-service products that use OS-level virtualization to deliver software in packages called containers. Containers are isolated from one another and bundle their own software, libraries, and configuration files; they can communicate with each other through well-defined channels.

### Containerization Concepts

Containerization is the process of packaging an application and its dependencies into a standardized unit called a container. This approach ensures that the application will run consistently regardless of the environment it's deployed to.

Key characteristics of containers:
- **Lightweight**: Containers share the host OS kernel, making them much smaller than virtual machines
- **Portable**: Containers can run on any system that supports Docker
- **Consistent**: Containers ensure that applications run the same way in development, testing, and production
- **Scalable**: Containers can be easily scaled up or down based on demand

### Virtual Machines vs Containers

| Aspect | Virtual Machines | Containers |
|--------|------------------|------------|
| Size | GBs | MBs |
| Startup Time | Minutes | Seconds |
| OS | Full OS | Shared OS Kernel |
| Isolation | Strong (Hypervisor) | Process-level |
| Performance | Lower (due to overhead) | Higher (native performance) |
| Resource Usage | Higher | Lower |

## Docker Architecture

Docker follows a client-server architecture with several key components:

### Docker Engine

The Docker Engine is the core component that runs containers. It consists of:

1. **Docker Daemon (dockerd)**: A long-running process that manages Docker objects like images, containers, networks, and volumes
2. **Docker Client (docker)**: The command-line interface that users interact with to communicate with the daemon
3. **REST API**: An API that allows programs to interact with the Docker daemon

### Communication Flow

1. The Docker client sends commands to the Docker daemon via the REST API
2. The Docker daemon processes these commands and manages Docker objects
3. Multiple Docker clients can communicate with a single daemon

### Docker Registry

A Docker registry stores Docker images. The default registry is Docker Hub, but private registries can also be used.

## Images vs Containers

### Docker Images

Docker images are read-only templates used to create containers. They contain:
- Application code
- Runtime environment
- Libraries and dependencies
- Environment variables
- Configuration files

Images are built from Dockerfiles and are layered. Each instruction in a Dockerfile creates a new layer in the image.

### Docker Containers

Containers are runnable instances of Docker images. They are:
- Isolated processes on the host OS
- Ephemeral by nature (state is lost when stopped)
- Can be started, stopped, moved, and deleted

### Lifecycle Relationship

1. **Build**: Create an image from a Dockerfile
2. **Pull**: Download an image from a registry
3. **Run**: Create and start a container from an image
4. **Stop**: Halt a running container
5. **Start**: Restart a stopped container
6. **Remove**: Delete a container or image

## Dockerfile Basics

A Dockerfile is a text file that contains instructions for building a Docker image.

### Basic Syntax

```dockerfile
# Comment
INSTRUCTION arguments
```

### Common Instructions

#### FROM

Specifies the base image:
```dockerfile
FROM ubuntu:20.04
```

#### RUN

Executes commands in a new layer:
```dockerfile
RUN apt-get update && apt-get install -y nginx
```

#### COPY

Copies files from host to image:
```dockerfile
COPY app.py /app/
```

#### ADD

Similar to COPY but with additional features (URLs, automatic extraction):
```dockerfile
ADD https://example.com/file.tar.gz /app/
```

#### WORKDIR

Sets the working directory:
```dockerfile
WORKDIR /app
```

#### EXPOSE

Documents which ports the container listens on:
```dockerfile
EXPOSE 8080
```

#### ENV

Sets environment variables:
```dockerfile
ENV NODE_ENV=production
```

#### CMD

Provides default command for the container:
```dockerfile
CMD ["python", "app.py"]
```

### Sample Dockerfile

```dockerfile
# Use an official Python runtime as the base image
FROM python:3.9-slim

# Set the working directory
WORKDIR /app

# Copy the requirements file
COPY requirements.txt .

# Install dependencies
RUN pip install -r requirements.txt

# Copy the application code
COPY . .

# Expose port
EXPOSE 5000

# Run the application
CMD ["python", "app.py"]
```

This Dockerfile creates a lightweight Python application container that can be built and run with Docker commands.
