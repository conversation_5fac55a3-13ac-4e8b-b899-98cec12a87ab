# Docker Data Management

Effective data management in Docker is essential for stateful applications, data persistence, and sharing information between containers. This guide covers volume types, backup strategies, data sharing, and database container management.

## Volume Types

Docker provides several mechanisms for data persistence, each with specific use cases and benefits.

### Named Volumes

Named volumes are managed by Docker and are the preferred mechanism for persisting data.

```bash
# Create a named volume
docker volume create mydata

# Inspect volume details
docker volume inspect mydata

# Run container with named volume
docker run -d \
  --name web \
  -v mydata:/usr/share/nginx/html \
  nginx

# List all volumes
docker volume ls

# Remove a volume
docker volume rm mydata

# Remove all unused volumes
docker volume prune
```

### Anonymous Volumes

Anonymous volumes are created automatically and are not given explicit names.

```bash
# Create container with anonymous volume
docker run -d \
  --name web \
  -v /usr/share/nginx/html \
  nginx

# Anonymous volumes are removed with container by default
docker rm -v web  # -v flag removes associated anonymous volumes

# Without -v flag, anonymous volumes become orphaned
docker rm web  # Anonymous volumes remain
```

### Bind Mounts

Bind mounts link container paths to host filesystem paths.

```bash
# Run with bind mount
docker run -d \
  --name web \
  -v /host/path:/container/path \
  nginx

# Run with read-only bind mount
docker run -d \
  --name web \
  -v /host/path:/container/path:ro \
  nginx

# In development, mount current directory
docker run -d \
  --name dev \
  -v $(pwd):/app \
  -w /app \
  node:alpine npm start
```

### tmpfs Mounts

tmpfs mounts store data in host memory (Linux only), useful for temporary data.

```bash
# Run with tmpfs mount
docker run -d \
  --name web \
  --tmpfs /tmp:rw,noexec,nosuid,size=100m \
  nginx
```

### Volume Drivers

Docker supports various volume drivers for specialized storage solutions.

```bash
# Create volume with specific driver
docker volume create \
  --driver local \
  --opt type=nfs \
  --opt o=addr=*************,rw \
  --opt device=:/path/to/dir \
  nfsvolume

# Use third-party volume drivers
docker volume create \
  --driver vieux/sshfs \
  --opt sshcmd=user@host:/path \
  --opt password=mypassword \
  sshvolume
```

## Data Backup and Restore

Implementing robust backup and restore strategies is critical for data protection.

### Backup Named Volumes

```bash
# Backup a named volume using a temporary container
docker run --rm \
  -v mydata:/source \
  -v $(pwd):/backup \
  alpine tar czf /backup/mydata-backup.tar.gz -C /source .

# Verify backup
tar tzf mydata-backup.tar.gz
```

### Restore Named Volumes

```bash
# Create new volume and restore data
docker volume create mydata-restored

docker run --rm \
  -v mydata-restored:/target \
  -v $(pwd):/backup \
  alpine tar xzf /backup/mydata-backup.tar.gz -C /target

# Use restored volume
docker run -d \
  --name web-restored \
  -v mydata-restored:/usr/share/nginx/html \
  nginx
```

### Automated Backup Script

```bash
#!/bin/bash
# backup-volumes.sh

VOLUMES=("db-data" "app-logs" "config-files")
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

for volume in "${VOLUMES[@]}"; do
  echo "Backing up volume: $volume"
  docker run --rm \
    -v $volume:/source \
    -v $BACKUP_DIR:/backup \
    alpine tar czf /backup/${volume}_$DATE.tar.gz -C /source .
done

echo "Backup completed: $BACKUP_DIR/*_$DATE.tar.gz"
```

### Database Backup

```bash
# Backup PostgreSQL database
docker exec postgres-db \
  pg_dump -U postgres mydb > backup.sql

# Backup MySQL database
docker exec mysql-db \
  mysqldump -u root -p mydb > backup.sql

# Backup using dedicated backup container
docker run --rm \
  -v db-data:/source \
  -v $(pwd):/backup \
  postgres:13 \
  pg_dump -h source -U postgres mydb > /backup/db-backup.sql
```

## Sharing Data Between Containers

Sharing data between containers enables collaborative and microservices architectures.

### Using Named Volumes for Sharing

```bash
# Create shared volume
docker volume create shared-data

# Run first container with shared volume
docker run -d \
  --name writer \
  -v shared-data:/data \
  alpine sh -c 'echo "Hello from writer" > /data/message.txt && sleep 3600'

# Run second container with same volume
docker run -it \
  --name reader \
  -v shared-data:/data \
  alpine cat /data/message.txt
```

### Using Bind Mounts for Sharing

```bash
# Create shared directory on host
mkdir -p /shared-data

# Run containers sharing host directory
docker run -d \
  --name writer \
  -v /shared-data:/data \
  alpine sh -c 'echo "Hello from writer" > /data/message.txt && sleep 3600'

docker run -it \
  --name reader \
  -v /shared-data:/data \
  alpine cat /data/message.txt
```

### Data-Only Containers (Legacy Pattern)

```bash
# Create data-only container (legacy approach)
docker create \
  -v /data \
  --name data-container \
  alpine

# Use data container in other containers
docker run -d \
  --name app1 \
  --volumes-from data-container \
  alpine sh -c 'echo "Data from app1" > /data/app1.txt && sleep 3600'

docker run -d \
  --name app2 \
  --volumes-from data-container \
  alpine cat /data/app1.txt
```

## Database Containers and Persistent Storage

Managing databases in containers requires special attention to data persistence and performance.

### PostgreSQL with Persistent Storage

```bash
# Create volume for PostgreSQL data
docker volume create postgres-data

# Run PostgreSQL with persistent storage
docker run -d \
  --name postgres-db \
  -e POSTGRES_DB=myapp \
  -e POSTGRES_USER=myuser \
  -e POSTGRES_PASSWORD=mypassword \
  -v postgres-data:/var/lib/postgresql/data \
  -p 5432:5432 \
  postgres:13

# Connect to database
docker exec -it postgres-db psql -U myuser -d myapp
```

### MySQL with Persistent Storage

```bash
# Create volume for MySQL data
docker volume create mysql-data

# Run MySQL with persistent storage
docker run -d \
  --name mysql-db \
  -e MYSQL_ROOT_PASSWORD=rootpassword \
  -e MYSQL_DATABASE=myapp \
  -e MYSQL_USER=myuser \
  -e MYSQL_PASSWORD=mypassword \
  -v mysql-data:/var/lib/mysql \
  -p 3306:3306 \
  mysql:8

# Connect to database
docker exec -it mysql-db mysql -u myuser -p myapp
```

### MongoDB with Persistent Storage

```bash
# Create volume for MongoDB data
docker volume create mongodb-data

# Run MongoDB with persistent storage
docker run -d \
  --name mongodb \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password \
  -v mongodb-data:/data/db \
  -p 27017:27017 \
  mongo:5

# Connect to database
docker exec -it mongodb mongosh -u admin -p password
```

### Redis with Persistent Storage

```bash
# Create volume for Redis data
docker volume create redis-data

# Run Redis with persistent storage
docker run -d \
  --name redis \
  -v redis-data:/data \
  -p 6379:6379 \
  redis:7 redis-server --appendonly yes

# Test Redis connection
docker exec -it redis redis-cli ping
```

### Database Initialization Scripts

```bash
# Directory structure for database initialization
# ./init/
#   ├── 01-create-tables.sql
#   ├── 02-insert-data.sql
#   └── 03-create-indexes.sql

# Run PostgreSQL with initialization scripts
docker run -d \
  --name postgres-db \
  -e POSTGRES_DB=myapp \
  -e POSTGRES_USER=myuser \
  -e POSTGRES_PASSWORD=mypassword \
  -v postgres-data:/var/lib/postgresql/data \
  -v $(pwd)/init:/docker-entrypoint-initdb.d \
  -p 5432:5432 \
  postgres:13
```

### Database Backup and Restore

```bash
# PostgreSQL backup
docker exec postgres-db \
  pg_dump -U myuser myapp > backup-$(date +%Y%m%d).sql

# PostgreSQL restore
docker exec -i postgres-db \
  psql -U myuser myapp < backup-$(date +%Y%m%d).sql

# MySQL backup
docker exec mysql-db \
  mysqldump -u myuser -p myapp > backup-$(date +%Y%m%d).sql

# MySQL restore
docker exec -i mysql-db \
  mysql -u myuser -p myapp < backup-$(date +%Y%m%d).sql
```

### Performance Considerations

```bash
# Use volume mounts for better I/O performance
docker run -d \
  --name db \
  -v /fast-ssd/db-data:/var/lib/postgresql/data \
  postgres:13

# For high-performance databases, consider device mapper
docker volume create \
  --driver local \
  --opt type=ext4 \
  --opt device=/dev/sdb1 \
  highperf-volume
```

This comprehensive guide to Docker data management provides DevOps engineers with the knowledge to implement robust data persistence, sharing, and backup strategies for containerized applications.
