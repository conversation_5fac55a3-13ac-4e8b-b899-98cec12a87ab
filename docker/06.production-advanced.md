# Docker Production & Advanced Topics

Deploying Docker containers in production environments requires careful consideration of security, monitoring, resource management, and orchestration. This guide covers essential practices for running Docker in production, including security best practices, health checks, resource constraints, registry management, and container orchestration basics.

## Security Best Practices

Security is paramount when running Docker in production environments.

### Non-Root Users

Running containers as non-root users reduces the impact of potential security breaches.

```dockerfile
# In Dockerfile
FROM node:18-alpine

# Create app user and group
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Set ownership of app files
COPY --chown=nextjs:nodejs . /app
WORKDIR /app
USER nextjs

CMD ["node", "server.js"]
```

At runtime:
```bash
# Run container as specific user
docker run -d --user 1001:1001 myapp

# Run with dropped capabilities
docker run -d --cap-drop=ALL --cap-add=NET_BIND_SERVICE myapp
```

### Image Scanning

Scan images for vulnerabilities before deployment:

```bash
# Using Docker Scout (built into Docker Desktop)
docker scout cves myapp:latest

# Using Trivy
trivy image myapp:latest

# Using Clair
clair-scanner myapp:latest

# In CI/CD pipeline
#!/bin/bash

docker build -t myapp:$COMMIT_SHA .

docker scout cves myapp:$COMMIT_SHA
if [ $? -ne 0 ]; then
  echo "Vulnerabilities found, failing build"
  exit 1
fi

docker push myapp:$COMMIT_SHA
```

### Secrets Management

Never hardcode secrets in images or Dockerfiles:

```bash
# Using Docker secrets (Swarm mode)
docker secret create db-password secrets/db-password.txt

docker service create \
  --name db \
  --secret db-password \
  -e POSTGRES_PASSWORD_FILE=/run/secrets/db-password \
  postgres:13

# Using environment files (for development only)
docker run -d --env-file .env myapp

# Using external secret management
docker run -d \
  -e DB_PASSWORD=$(vault read -field=password secret/myapp) \
  myapp
```

### Security Configuration

```bash
# Run with read-only root filesystem
docker run -d --read-only myapp

# Run with specific security options
docker run -d \
  --security-opt=no-new-privileges \
  --security-opt=label=type:container_runtime_t \
  myapp

# Drop all capabilities except those needed
docker run -d \
  --cap-drop=ALL \
  --cap-add=NET_BIND_SERVICE \
  myapp
```

## Health Checks and Monitoring

Implementing health checks and monitoring ensures application reliability.

### Docker Health Checks

```dockerfile
# In Dockerfile
FROM node:18

COPY . /app
WORKDIR /app
RUN npm install

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000
CMD ["npm", "start"]
```

At runtime:
```bash
# Override health check
docker run -d \
  --health-cmd="curl -f http://localhost:3000/health || exit 1" \
  --health-interval=30s \
  --health-timeout=3s \
  --health-retries=3 \
  myapp
```

### Container Monitoring

```bash
# Monitor container resource usage
docker stats

docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

# Monitor specific containers
docker stats web db cache

# Log monitoring
docker logs -f web

docker logs --since 1h web
```

### Logging Drivers

```bash
# Use syslog driver
docker run -d --log-driver=syslog --log-opt syslog-address=tcp://************:123 myapp

# Use json-file with size limits
docker run -d \
  --log-driver=json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  myapp

# Use fluentd for centralized logging
docker run -d \
  --log-driver=fluentd \
  --log-opt fluentd-address=************:24224 \
  myapp
```

## Resource Management

Properly managing container resources prevents resource exhaustion and ensures performance.

### CPU Constraints

```bash
# Limit CPU shares (relative weight)
docker run -d --cpu-shares=512 myapp

# Limit CPU quota (absolute limit)
docker run -d --cpus="1.5" myapp  # 1.5 CPUs

# Pin to specific CPUs
docker run -d --cpuset-cpus="0,1" myapp

# In docker-compose.yml
version: "3.8"
services:
  web:
    image: myapp
    deploy:
      resources:
        limits:
          cpus: '1.5'
        reservations:
          cpus: '0.5'
```

### Memory Constraints

```bash
# Limit memory usage
docker run -d --memory="512m" myapp

# Set memory swap
docker run -d --memory="512m" --memory-swap="1g" myapp

# Set memory reservation (soft limit)
docker run -d --memory="512m" --memory-reservation="256m" myapp

# Prevent memory issues with OOM killer
docker run -d --memory="512m" --oom-kill-disable myapp

# In docker-compose.yml
version: "3.8"
services:
  web:
    image: myapp
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

### Block I/O Constraints

```bash
# Limit block I/O bandwidth
docker run -d --blkio-weight=500 myapp

# Limit write IOPS
docker run -d --device-write-iops=/dev/sda:1000 myapp

# Limit read BPS
docker run -d --device-read-bps=/dev/sda:1mb myapp
```

## Docker Registry

Managing images in registries is essential for distribution and deployment.

### Docker Hub

```bash
# Login to Docker Hub
docker login

# Tag image for Docker Hub
docker tag myapp:latest username/myapp:latest

# Push to Docker Hub
docker push username/myapp:latest

# Pull from Docker Hub
docker pull username/myapp:latest
```

### Private Registries

```bash
# Login to private registry
docker login registry.example.com

# Tag for private registry
docker tag myapp:latest registry.example.com/myapp:latest

# Push to private registry
docker push registry.example.com/myapp:latest

# Pull from private registry
docker pull registry.example.com/myapp:latest
```

### Registry Management

```bash
# Run private registry
docker run -d -p 5000:5000 --name registry registry:2

# Tag for local registry
docker tag myapp:latest localhost:5000/myapp:latest

# Push to local registry
docker push localhost:5000/myapp:latest

# Configure Docker daemon for insecure registry
# /etc/docker/daemon.json
{
  "insecure-registries" : ["registry.example.com"]
}
```

### Image Lifecycle Management

```bash
# Automated image tagging in CI/CD
#!/bin/bash
VERSION=$(git describe --tags --always)
docker build -t myapp:$VERSION .
docker tag myapp:$VERSION myapp:latest

docker push myapp:$VERSION
docker push myapp:latest

# Clean up old images
#!/bin/bash
OLD_IMAGES=$(docker images --format "{{.ID}}\t{{.Repository}}\t{{.CreatedAt}}" | \
  awk '$3 < "'$(date -d '30 days ago' +%s)'" && $2 != "<none>" {print $1}')
if [ -n "$OLD_IMAGES" ]; then
  docker rmi $OLD_IMAGES
fi
```

## Container Orchestration Basics

Understanding orchestration is essential for managing containerized applications at scale.

### Docker Swarm

```bash
# Initialize Swarm mode
docker swarm init

# Create service
docker service create \
  --name web \
  --replicas 3 \
  --publish 80:80 \
  nginx

# Scale service
docker service scale web=5

# Update service
docker service update \
  --image nginx:alpine \
  web

# List services
docker service ls

# Inspect service
docker service inspect web
```

### Kubernetes Basics

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web
spec:
  replicas: 3
  selector:
    matchLabels:
      app: web
  template:
    metadata:
      labels:
        app: web
    spec:
      containers:
      - name: web
        image: nginx:alpine
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: web
spec:
  selector:
    app: web
  ports:
  - port: 80
    targetPort: 80
  type: LoadBalancer
```

```bash
# Deploy to Kubernetes
kubectl apply -f deployment.yaml

# Scale deployment
kubectl scale deployment web --replicas=5

# Update deployment
kubectl set image deployment/web web=nginx:latest
```

### Orchestration Comparison

| Feature | Docker Swarm | Kubernetes |
|---------|--------------|------------|
| Complexity | Low | High |
| Learning Curve | Gentle | Steep |
| Setup | Single command | Complex |
| Scaling | Native | Native |
| Service Discovery | Built-in | Built-in |
| Load Balancing | Built-in | Built-in |
| Storage Orchestration | Limited | Extensive |
| Networking | Simple | Advanced |
| Extensibility | Limited | Highly Extensible |

### Health Checks in Orchestration

```yaml
# Docker Swarm service with health check
version: "3.8"
services:
  web:
    image: myapp
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        failure_action: rollback
      rollback_config:
        parallelism: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
```

This comprehensive guide to Docker production and advanced topics provides DevOps engineers with the knowledge to securely deploy, monitor, and manage containerized applications at scale.
