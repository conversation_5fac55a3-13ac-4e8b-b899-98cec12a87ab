# Docker Resource Management

Effective resource management is crucial for optimizing container performance, preventing resource exhaustion, and ensuring efficient utilization of host system resources. This guide covers CPU, memory, storage, and network resource management in Docker environments.

## CPU Resource Management

Proper CPU allocation ensures fair resource distribution and prevents containerized applications from monopolizing CPU resources.

### CPU Shares (Relative Weight)

CPU shares provide relative weighting for CPU access when contention occurs:

```bash
# Set CPU shares (default is 1024)
docker run -d --cpu-shares=512 myapp  # Half the default weight

docker run -d --cpu-shares=2048 myapp  # Double the default weight

# In docker-compose.yml
version: "3.8"
services:
  web:
    image: myapp
    deploy:
      resources:
        reservations:
          cpus: '0.5'
        limits:
          cpus: '1.0'
```

### CPU Quota (Absolute Limit)

CPU quota sets absolute CPU time limits for containers:

```bash
# Limit to 1.5 CPUs
docker run -d --cpus="1.5" myapp

# Equivalent to:
docker run -d --cpu-period=100000 --cpu-quota=150000 myapp

# Limit to 50% of one CPU
docker run -d --cpus=".5" myapp

# In docker-compose.yml
version: "3.8"
services:
  web:
    image: myapp
    cpus: 1.5
```

### CPU Pinning

Pin containers to specific CPU cores for performance-critical applications:

```bash
# Pin to specific CPUs
docker run -d --cpuset-cpus="0,1" myapp

# Pin to CPU range
docker run -d --cpuset-cpus="0-3" myapp

# Pin to specific CPU cores with memory nodes
docker run -d --cpuset-cpus="0,1" --cpuset-mems="0" myapp

# In docker-compose.yml
version: "3.8"
services:
  web:
    image: myapp
    environment:
      - GOMAXPROCS=2
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 1G
```

### CPU Monitoring

Monitor CPU usage to optimize resource allocation:

```bash
# Monitor container CPU usage
docker stats

docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Monitor specific containers
docker stats web db cache

# Use cgroups for detailed CPU metrics
docker exec web cat /sys/fs/cgroup/cpu/cpu.stat

docker exec web cat /sys/fs/cgroup/cpu/cpuacct.usage
```

## Memory Resource Management

Memory management prevents containers from consuming excessive memory and causing system instability.

### Memory Limits

Set memory limits to prevent container memory exhaustion:

```bash
# Set memory limit
docker run -d --memory="512m" myapp

# Set memory limit with swap
docker run -d --memory="512m" --memory-swap="1g" myapp

# Set memory limit without swap
docker run -d --memory="512m" --memory-swap="512m" myapp

# Set memory reservation (soft limit)
docker run -d --memory="512m" --memory-reservation="256m" myapp

# Prevent OOM killer
docker run -d --memory="512m" --oom-kill-disable myapp
```

### Memory Swappiness

Control memory swapping behavior:

```bash
# Disable swapping (0-100, 0 disables swapping)
docker run -d --memory-swappiness=0 myapp

# Default swapping behavior
docker run -d --memory-swappiness=60 myapp

# In docker-compose.yml
version: "3.8"
services:
  web:
    image: myapp
    mem_limit: 512m
    mem_reservation: 256m
    mem_swappiness: 0
```

### Memory Monitoring

Monitor memory usage to optimize allocation:

```bash
# Monitor container memory usage
docker stats

docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# Check memory details
docker exec web cat /sys/fs/cgroup/memory/memory.usage_in_bytes

docker exec web cat /sys/fs/cgroup/memory/memory.limit_in_bytes

docker exec web cat /sys/fs/cgroup/memory/memory.stat
```

## Storage Resource Management

Storage management ensures efficient disk space utilization and I/O performance.

### Storage Drivers

Choose appropriate storage drivers for your use case:

```bash
# Check current storage driver
docker info | grep "Storage Driver"

# Configure storage driver in daemon.json
{
  "storage-driver": "overlay2"
}

# For high-performance workloads
docker info | grep "Backing Filesystem"
```

### Volume Management

Efficiently manage Docker volumes for persistent storage:

```bash
# Create named volumes
docker volume create app-data

docker volume create --driver local \
  --opt type=none \
  --opt device=/ssd/data \
  --opt o=bind \
  fast-volume

# Run container with volume
docker run -d \
  -v app-data:/var/lib/mysql \
  -v /ssd/logs:/var/log \
  mysql:8

# Monitor volume usage
docker system df -v

docker volume ls

docker volume inspect app-data
```

### Storage Quotas

Implement storage quotas to prevent disk space exhaustion:

```bash
# Use device mapper for storage quotas
# Configure in daemon.json
{
  "storage-driver": "devicemapper",
  "storage-opts": [
    "dm.basesize=20G",
    "dm.loopdatasize=30G",
    "dm.loopmetadatasize=2G"
  ]
}

# For XFS with project quotas
{
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.size=10G"
  ]
}
```

### Storage Monitoring

Monitor storage usage to prevent disk space issues:

```bash
# Check Docker disk usage
docker system df
docker system df -v

# Clean up unused data
docker system prune

docker volume prune

docker image prune

# Monitor specific container disk usage
docker exec web df -h
```

## Block I/O Resource Management

Control block I/O to prevent containers from monopolizing disk I/O resources.

### I/O Bandwidth Limits

Limit block I/O bandwidth for containers:

```bash
# Set block I/O weight (10-1000, default 500)
docker run -d --blkio-weight=300 myapp

# Limit write IOPS
docker run -d \
  --device-write-iops=/dev/sda:1000 \
  myapp

# Limit read IOPS
docker run -d \
  --device-read-iops=/dev/sda:1000 \
  myapp

# Limit write BPS
docker run -d \
  --device-write-bps=/dev/sda:1mb \
  myapp

# Limit read BPS
docker run -d \
  --device-read-bps=/dev/sda:1mb \
  myapp
```

### I/O Monitoring

Monitor I/O performance to optimize resource allocation:

```bash
# Monitor container I/O
docker stats

docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# Check I/O details
docker exec web iotop -b -n 1

docker exec web iostat -x 1 1
```

## Network Resource Management

Manage network resources to ensure optimal network performance and prevent bandwidth exhaustion.

### Network Bandwidth Limits

Limit network bandwidth for containers:

```bash
# Use tc (traffic control) for bandwidth limiting
# This requires running container in privileged mode
docker run -d --privileged myapp

docker exec web tc qdisc add dev eth0 root tbf rate 1mbit burst 32kbit latency 400ms

docker exec web tc qdisc show dev eth0

# Remove bandwidth limit
docker exec web tc qdisc del dev eth0 root
```

### Network Monitoring

Monitor network usage to optimize performance:

```bash
# Monitor container network usage
docker stats

docker stats --format "table {{.Container}}\t{{.NetIO}}"

# Check network details
docker exec web cat /proc/net/dev

docker exec web netstat -i
```

## Resource Constraints in Orchestration

Implement resource management in container orchestration platforms.

### Docker Swarm

```yaml
# docker-compose.yml for Docker Swarm
version: "3.8"
services:
  web:
    image: myapp
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      placement:
        constraints:
          - node.role == worker
```

### Kubernetes

```yaml
# Kubernetes resource management
apiVersion: v1
kind: Pod
metadata:
  name: myapp
spec:
  containers:
  - name: web
    image: myapp
    resources:
      requests:
        memory: "256Mi"
        cpu: "0.5"
      limits:
        memory: "512Mi"
        cpu: "1.0"
    env:
    - name: GOMAXPROCS
      value: "1"
```

## Resource Optimization Strategies

Implement strategies for optimal resource utilization.

### Right-Sizing Containers

```bash
# Monitor resource usage over time
#!/bin/bash

echo "Monitoring resource usage for 1 hour..."
for i in {1..60}; do
  docker stats --no-stream \
    --format "{{.Container}} {{.CPUPerc}} {{.MemPerc}}" \
    >> resource-usage.log
  sleep 60
done

echo "Analyzing resource usage..."
# Process log file to determine optimal resource allocation
```

### Auto-scaling

```yaml
# Docker Swarm auto-scaling
version: "3.8"
services:
  web:
    image: myapp
    deploy:
      mode: replicated
      replicas: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
      update_config:
        parallelism: 1
        delay: 10s
      rollback_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
```

### Resource Profiling

```bash
# Profile application resource usage
#!/bin/bash

CONTAINER_ID=$(docker create myapp)

docker start $CONTAINER_ID

# Monitor CPU and memory
for i in {1..10}; do
  docker exec $CONTAINER_ID top -b -n 1 | head -20
  docker exec $CONTAINER_ID free -m
  sleep 5
done

docker stop $CONTAINER_ID
docker rm $CONTAINER_ID
```

## Resource Management Best Practices

Follow best practices for effective resource management.

### Monitoring and Alerting

```bash
# Create resource monitoring script
#!/bin/bash

THRESHOLD_CPU=80
THRESHOLD_MEM=80

check_resources() {
  docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemPerc}}" | \
  tail -n +2 | \
  while read container cpu mem; do
    cpu_val=$(echo $cpu | sed 's/%//')
    mem_val=$(echo $mem | sed 's/%//')
    
    if (( $(echo "$cpu_val > $THRESHOLD_CPU" | bc -l) )); then
      echo "WARNING: High CPU usage on $container: $cpu"
    fi
    
    if (( $(echo "$mem_val > $THRESHOLD_MEM" | bc -l) )); then
      echo "WARNING: High memory usage on $container: $mem"
    fi
  done
}

check_resources
```

### Resource Cleanup

```bash
# Automated cleanup script
#!/bin/bash

# Remove stopped containers
docker container prune -f

# Remove unused networks
docker network prune -f

# Remove unused volumes
docker volume prune -f

# Remove unused images
docker image prune -a -f

# Remove build cache
docker builder prune -f
```

This comprehensive guide to Docker resource management provides DevOps engineers with the knowledge to effectively manage CPU, memory, storage, and network resources for containerized applications, ensuring optimal performance and resource utilization.
