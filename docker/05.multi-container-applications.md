# Multi-Container Applications with Docker Compose

Docker Compose is a tool for defining and running multi-container Docker applications. With Compose, you use a YAML file to configure your application's services, networks, and volumes, then with a single command, you create and start all the services from your configuration.

## Docker Compose YAML Syntax

The docker-compose.yml file is the heart of a Compose application. It defines services, networks, and volumes in a declarative format.

### Basic Structure

```yaml
version: "3.8"  # Version of Compose file format

services:       # Definition of application services
  # Service definitions here

networks:       # Custom networks (optional)
  # Network definitions here

volumes:        # Named volumes (optional)
  # Volume definitions here
```

### Service Definitions

```yaml
version: "3.8"

services:
  web:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./html:/usr/share/nginx/html
    networks:
      - frontend
    depends_on:
      - api

  api:
    build: ./api
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=******************************/myapp
    networks:
      - frontend
      - backend
    depends_on:
      - db

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=myapp
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - db-data:/var/lib/postgresql/data
    networks:
      - backend

networks:
  frontend:
  backend:

volumes:
  db-data:
```

## Service Orchestration

Compose provides powerful features for orchestrating services in the right order and with appropriate policies.

### depends_on

Control service startup order:

```yaml
version: "3.8"
services:
  web:
    image: nginx
    depends_on:
      - api
  api:
    image: node:alpine
    depends_on:
      - db
      - cache
  db:
    image: postgres:13
  cache:
    image: redis:alpine
```

### Restart Policies

Control container restart behavior:

```yaml
version: "3.8"
services:
  web:
    image: nginx
    restart: "no"     # Never restart (default)
  api:
    image: node:alpine
    restart: always    # Always restart
  worker:
    image: myworker
    restart: on-failure  # Restart on failure only
  db:
    image: postgres:13
    restart: unless-stopped  # Restart unless explicitly stopped
```

### Health Checks

Define health checks for services:

```yaml
version: "3.8"
services:
  web:
    image: nginx
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
  db:
    image: postgres:13
    environment:
      - POSTGRES_PASSWORD=password
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
```

## Environment Management

Managing multiple environments (development, staging, production) with Compose.

### Base Configuration

```yaml
# docker-compose.yml
version: "3.8"
services:
  web:
    build: .
    ports:
      - "${PORT:-3000}:${PORT:-3000}"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ./src:/app/src  # Only for development

  db:
    image: postgres:13
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - db-data:/var/lib/postgresql/data

volumes:
  db-data:
```

### Override Files

```yaml
# docker-compose.override.yml (for development)
version: "3.8"
services:
  web:
    volumes:
      - ./src:/app/src
    environment:
      - NODE_ENV=development
    ports:
      - "3000:3000"
```

```yaml
# docker-compose.prod.yml (for production)
version: "3.8"
services:
  web:
    environment:
      - NODE_ENV=production
    ports:
      - "80:3000"
    volumes: []  # Remove development volume
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '0.50'
          memory: 512M
```

### Using Multiple Compose Files

```bash
# Development (uses docker-compose.yml + docker-compose.override.yml)
docker-compose up

# Production
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Staging
docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
```

## Scaling Services

Compose allows you to scale services up or down easily.

### Scaling with CLI

```bash
# Scale web service to 3 replicas
docker-compose up -d --scale web=3

# Scale multiple services
docker-compose up -d --scale web=3 --scale worker=5

# Scale down
docker-compose up -d --scale web=1
```

### Scaling in Production

```yaml
# docker-compose.prod.yml
version: "3.8"
services:
  web:
    image: myapp:latest
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
  worker:
    image: myapp-worker:latest
    deploy:
      replicas: 5
```

### Load Balancing with Scaling

```yaml
version: "3.8"
services:
  lb:
    image: dockercloud/haproxy
    links:
      - web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    ports:
      - "80:80"
  web:
    image: nginx
    # Scale this service to multiple instances
```

Then scale:
```bash
docker-compose up -d
docker-compose scale web=5
```

## Advanced Compose Features

### Extending Services

```yaml
# docker-compose.yml
version: "3.8"
services:
  web:
    image: nginx:alpine
    ports:
      - "80:80"
```

```yaml
# docker-compose.prod.yml
version: "3.8"
services:
  web:
    extends:
      file: docker-compose.yml
      service: web
    ports:
      - "80:80"
      - "443:443"
```

### External Resources

```yaml
version: "3.8"
services:
  web:
    image: nginx
    networks:
      - frontend

networks:
  frontend:
    external: true
    name: my-external-network

volumes:
  db-data:
    external: true
    name: my-external-volume
```

### Secrets Management

```yaml
version: "3.8"
services:
  db:
    image: postgres:13
    environment:
      - POSTGRES_PASSWORD_FILE=/run/secrets/db-password
    secrets:
      - db-password

secrets:
  db-password:
    file: ./secrets/db-password.txt
```

### Configs

```yaml
version: "3.8"
services:
  app:
    image: nginx
    configs:
      - source: nginx-config
        target: /etc/nginx/conf.d/default.conf

configs:
  nginx-config:
    file: ./config/nginx.conf
```

This comprehensive guide to multi-container applications with Docker Compose provides DevOps engineers with the knowledge to define, deploy, and manage complex applications using Docker's orchestration capabilities.