# Intermediate IaC Topics

As you advance in your Infrastructure as Code (IaC) journey, you'll encounter more complex challenges that require sophisticated approaches to state management, modularity, environment isolation, security, testing, and deployment automation. This guide covers these essential intermediate topics.

## State Management

State management is one of the most critical aspects of IaC, particularly with tools like Terraform that maintain state files to track the current infrastructure state.

### What is State?

State is a mapping of real-world resources to configuration files. It allows IaC tools to know what resources exist and how they were configured.

### Local vs Remote State

#### Local State

By default, Terraform stores state in a local file named `terraform.tfstate`:

```hcl
# No backend configuration required for local state
# State stored in terraform.tfstate in the current directory
```

#### Remote State

For team collaboration and production use, remote state storage is essential:

```hcl
# Backend configuration for remote state
terraform {
  backend "s3" {
    bucket = "my-terraform-state-bucket"
    key    = "production/terraform.tfstate"
    region = "us-west-2"
    dynamodb_table = "terraform-state-lock"
  }
}
```

### State Locking

State locking prevents concurrent modifications that could cause conflicts:

```hcl
# S3 backend with DynamoDB locking
terraform {
  backend "s3" {
    bucket         = "my-terraform-state-bucket"
    key            = "production/terraform.tfstate"
    region         = "us-west-2"
    dynamodb_table = "terraform-state-lock"
    encrypt        = true
  }
}
```

### State Operations

Common state management commands:

```bash
# Refresh state to match real infrastructure
terraform refresh

# Show current state
terraform show

# List resources in state
terraform state list

# Move resources between state files
terraform state mv aws_instance.web aws_instance.web_server

# Remove resources from state (without destroying)
terraform state rm aws_instance.web
```

### State File Security

Protect state files as they may contain sensitive information:

1. Encrypt state files at rest
2. Restrict access to state files
3. Use remote backends with encryption
4. Enable versioning for state files

## Modules and Reusability

Modules are containers for multiple resources that are used together, enabling reusable and composable infrastructure.

### Module Structure

```
modules/
├── network/
│   ├── main.tf
│   ├── variables.tf
│   ├── outputs.tf
│   └── README.md
├── compute/
│   ├── main.tf
│   ├── variables.tf
│   ├── outputs.tf
│   └── README.md
└── storage/
    ├── main.tf
    ├── variables.tf
    ├── outputs.tf
    └── README.md
```

### Creating a Module

Example network module:

```hcl
# modules/network/main.tf
resource "aws_vpc" "main" {
  cidr_block = var.vpc_cidr
  
  tags = {
    Name = "${var.environment}-vpc"
  }
}

resource "aws_subnet" "public" {
  count = length(var.public_subnets)
  
  vpc_id            = aws_vpc.main.id
  cidr_block        = var.public_subnets[count.index]
  availability_zone = var.availability_zones[count.index]
  
  tags = {
    Name = "${var.environment}-public-${count.index}"
  }
}
```

```hcl
# modules/network/variables.tf
variable "environment" {
  description = "Environment name"
  type        = string
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnets" {
  description = "List of public subnet CIDR blocks"
  type        = list(string)
}

variable "availability_zones" {
  description = "List of availability zones"
  type        = list(string)
}
```

```hcl
# modules/network/outputs.tf
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "public_subnet_ids" {
  description = "IDs of public subnets"
  value       = aws_subnet.public[*].id
}
```

### Using a Module

```hcl
# main.tf
module "network" {
  source = "./modules/network"
  
  environment        = "production"
  vpc_cidr           = "10.0.0.0/16"
  public_subnets     = ["********/24", "********/24"]
  availability_zones = ["us-west-2a", "us-west-2b"]
}

output "vpc_id" {
  value = module.network.vpc_id
}
```

### Module Best Practices

1. **Version your modules**: Use version control tags
2. **Document your modules**: Include README files with examples
3. **Use variables for customization**: Make modules flexible
4. **Output important values**: Enable module consumers to reference key resources
5. **Validate inputs**: Use variable validation to catch errors early

## Environment Management

Managing multiple environments (dev, staging, prod) requires careful planning to ensure consistency while allowing for environment-specific configurations.

### Directory Structure Approach

```
environments/
├── dev/
│   ├── main.tf
│   ├── variables.tf
│   ├── terraform.tfvars
│   └── backend.tf
├── staging/
│   ├── main.tf
│   ├── variables.tf
│   ├── terraform.tfvars
│   └── backend.tf
└── prod/
    ├── main.tf
    ├── variables.tf
    ├── terraform.tfvars
    └── backend.tf
```

### Shared Modules Approach

```hcl
# environments/dev/main.tf
module "infrastructure" {
  source = "../../modules/infrastructure"
  
  environment = "dev"
  instance_type = "t2.micro"
  min_instances = 1
  max_instances = 3
}
```

```hcl
# environments/prod/main.tf
module "infrastructure" {
  source = "../../modules/infrastructure"
  
  environment = "prod"
  instance_type = "t3.large"
  min_instances = 3
  max_instances = 10
}
```

### Environment-Specific Variables

```hcl
# environments/dev/terraform.tfvars
region        = "us-west-2"
instance_type = "t2.micro"
min_instances = 1
max_instances = 3
```

```hcl
# environments/prod/terraform.tfvars
region        = "us-west-2"
instance_type = "t3.large"
min_instances = 3
max_instances = 10
```

### Workspace Approach (Terraform)

```bash
# Create workspaces
terraform workspace new dev
terraform workspace new staging
terraform workspace new prod

# Switch between workspaces
terraform workspace select dev

# Use workspace name in configurations
resource "aws_instance" "web" {
  ami           = "ami-0c55b159cbfafe1d0"
  instance_type = terraform.workspace == "prod" ? "t3.large" : "t2.micro"
  
  tags = {
    Environment = terraform.workspace
  }
}
```

## Security Best Practices

Security should be integrated into every aspect of your IaC implementation.

### Secrets Management

Never store secrets directly in IaC code:

```hcl
# BAD: Hardcoded secrets
resource "aws_db_instance" "main" {
  username = "admin"
  password = "supersecretpassword123"
}
```

```hcl
# GOOD: Use variables or secret managers
resource "aws_db_instance" "main" {
  username = var.db_username
  password = var.db_password  # Injected at runtime
}

# Or use AWS Secrets Manager
data "aws_secretsmanager_secret_version" "db_creds" {
  secret_id = "production/database-credentials"
}

resource "aws_db_instance" "main" {
  username = jsondecode(data.aws_secretsmanager_secret_version.db_creds.secret_string)["username"]
  password = jsondecode(data.aws_secretsmanager_secret_version.db_creds.secret_string)["password"]
}
```

### Least Privilege Access

Create specific IAM policies for IaC tools:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ec2:RunInstances",
        "ec2:TerminateInstances",
        "ec2:CreateSecurityGroup",
        "ec2:DeleteSecurityGroup"
      ],
      "Resource": "*"
    }
  ]
}
```

### Infrastructure Hardening

Implement security controls in your IaC definitions:

```hcl
# Enable encryption
resource "aws_ebs_volume" "example" {
  availability_zone = "us-west-2a"
  size              = 40
  encrypted         = true
  kms_key_id        = aws_kms_key.ebs.arn
}

# Restrict SSH access
resource "aws_security_group" "ssh" {
  name        = "ssh-access"
  description = "Allow SSH access from specific IPs"

  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/8"]  # Restrict to internal network only
  }
}
```

## Testing Infrastructure Code

Testing IaC ensures reliability, security, and compliance before deployment.

### Validation Testing

Check syntax and basic configuration:

```bash
# Terraform validation
terraform validate

# Linting with tflint
tflint

# CloudFormation validation
aws cloudformation validate-template \
  --template-body file://template.yaml
```

### Unit Testing

Test individual modules and components:

```python
# Using pytest with pytest-terraform
import pytest
from pytest_terraform import terraform

@terraform("network", scope="session")
def test_vpc_creation(terraform_output):
    vpc_id = terraform_output["vpc_id"]
    assert vpc_id is not None
    assert vpc_id.startswith("vpc-")
```

### Integration Testing

Test complete infrastructure deployments:

```python
# Using Terratest (Go)
func TestTerraformAwsHelloWorldExample(t *testing.T) {
  terraformOptions := &terraform.Options{
    TerraformDir: "../examples/terraform-aws-hello-world-example",
  }
  
  defer terraform.Destroy(t, terraformOptions)
  terraform.InitAndApply(t, terraformOptions)
  
  instanceID := terraform.Output(t, terraformOptions, "instance_id")
  assert.NotEmpty(t, instanceID)
}
```

### Security Testing

Scan for security vulnerabilities:

```bash
# Using tfsec for Terraform security scanning
tfsec .

# Using checkov for multi-tool scanning
checkov -d .

# Using kubeaudit for Kubernetes security
kubeaudit all
```

## CI/CD Pipelines for Infrastructure Deployment

Automating Iaform deployments through CI/CD pipelines ensures consistent, reliable infrastructure changes.

### Pipeline Stages

1. **Validate**: Check syntax and basic configuration
2. **Plan**: Generate execution plan
3. **Test**: Run automated tests
4. **Approve**: Manual approval for production
5. **Apply**: Deploy infrastructure changes

### GitHub Actions Example

```yaml
# .github/workflows/terraform.yml
name: Terraform CI/CD

on:
  push:
    branches:
      - main

jobs:
  terraform:
    name: Terraform
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2

      - name: Terraform Format
        run: terraform fmt -check

      - name: Terraform Init
        run: terraform init

      - name: Terraform Validate
        run: terraform validate

      - name: Terraform Plan
        run: terraform plan -no-color
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Terraform Apply
        if: github.ref == 'refs/heads/main'
        run: terraform apply -auto-approve
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
```

### Jenkins Pipeline Example

```groovy
pipeline {
  agent any
  
  environment {
    AWS_DEFAULT_REGION = 'us-west-2'
  }
  
  stages {
    stage('Validate') {
      steps {
        sh 'terraform validate'
      }
    }
    
    stage('Plan') {
      steps {
        sh 'terraform plan -out=tfplan'
      }
    }
    
    stage('Approve') {
      steps {
        input message: 'Approve deployment?', ok: 'Deploy'
      }
    }
    
    stage('Apply') {
      steps {
        sh 'terraform apply -auto-approve tfplan'
      }
    }
  }
}
```

### GitLab CI Example

```yaml
# .gitlab-ci.yml
stages:
  - validate
  - plan
  - deploy

variables:
  TF_ROOT: ${CI_PROJECT_DIR}
  
terraform-validate:
  stage: validate
  script:
    - terraform validate
  
terraform-plan:
  stage: plan
  script:
    - terraform plan -out=tfplan
  artifacts:
    paths:
      - tfplan
  
terraform-apply:
  stage: deploy
  script:
    - terraform apply -auto-approve tfplan
  when: manual
  only:
    - main
```

## Drift Detection and Management

Infrastructure drift occurs when the actual state of infrastructure differs from the defined IaC configuration.

### Detection Methods

```bash
# Terraform drift detection
terraform plan  # Shows differences between state and actual infrastructure

terraform refresh  # Updates state to match actual infrastructure
```

### Prevention Strategies

1. **Regular audits**: Schedule periodic plan executions
2. **Immutable infrastructure**: Replace rather than modify resources
3. **Policy enforcement**: Use policy as code tools to prevent unauthorized changes
4. **Monitoring alerts**: Set up alerts for unauthorized changes

## Conclusion

Mastering these intermediate IaC topics is crucial for building robust, secure, and maintainable infrastructure. State management ensures consistency, modules promote reusability, environment management provides isolation, security best practices protect your infrastructure, testing ensures reliability, and CI/CD pipelines automate deployment. As you implement these practices, you'll be well-prepared for advanced IaC concepts and patterns.