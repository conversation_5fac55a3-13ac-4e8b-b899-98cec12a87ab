# Core IaC Tools and Technologies

Infrastructure as Code (IaC) tools enable teams to manage infrastructure through code rather than manual processes. This guide covers the major categories of IaC tools: declarative tools and configuration management tools.

## Declarative IaC Tools

Declarative tools describe the desired end state of infrastructure, and the tool figures out how to achieve that state.

### Terraform

Terraform by HashiCorp is the most popular multi-cloud IaC tool, supporting over 1000 providers including AWS, Azure, GCP, and more.

#### Key Features

- **Multi-cloud support**: Single tool for multiple cloud providers
- **Declarative syntax**: Describe desired state, not steps
- **State management**: Tracks current infrastructure state
- **Modules**: Reusable infrastructure components
- **Providers**: Extensible to support new platforms

#### Basic Terraform Workflow

1. **Write**: Define infrastructure in HCL (HashiCorp Configuration Language)
2. **Plan**: Preview changes before applying
3. **Apply**: Create, update, or destroy infrastructure

#### Example Terraform Configuration

```hcl
# main.tf
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.0"
    }
  }
}

provider "aws" {
  region = "us-west-2"
}

resource "aws_vpc" "main" {
  cidr_block = "10.0.0.0/16"
  
  tags = {
    Name = "Main VPC"
  }
}

resource "aws_subnet" "public" {
  vpc_id     = aws_vpc.main.id
  cidr_block = "********/24"
  
  tags = {
    Name = "Public Subnet"
  }
}

resource "aws_instance" "web" {
  ami           = "ami-0c55b159cbfafe1d0"
  instance_type = "t2.micro"
  subnet_id     = aws_subnet.public.id
  
  tags = {
    Name = "Web Server"
  }
}
```

#### Terraform Commands

```bash
# Initialize Terraform working directory
terraform init

# Preview infrastructure changes
terraform plan

# Apply infrastructure changes
terraform apply

# Destroy infrastructure
terraform destroy
```

### AWS CloudFormation

CloudFormation is AWS's native IaC service that uses YAML or JSON templates to provision and manage AWS resources.

#### Key Features

- **AWS-native**: Deep integration with AWS services
- **Template-based**: Define infrastructure in JSON or YAML
- **Stack management**: Manage related resources as a unit
- **Change sets**: Preview changes before applying

#### Example CloudFormation Template

```yaml
# web-server.yaml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'Simple web server stack'

Parameters:
  InstanceType:
    Type: String
    Default: t2.micro
    AllowedValues:
      - t2.micro
      - t3.small
      - t3.medium
    Description: EC2 instance type

Resources:
  WebServer:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: ami-0c55b159cbfafe1d0
      InstanceType: !Ref InstanceType
      Tags:
        - Key: Name
          Value: Web Server

  WebServerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for web server
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0

Outputs:
  WebServerPublicIP:
    Description: Public IP of the web server
    Value: !GetAtt WebServer.PublicIp
```

#### CloudFormation Commands

```bash
# Create a stack
aws cloudformation create-stack \
  --stack-name web-server-stack \
  --template-body file://web-server.yaml

# Update a stack
aws cloudformation update-stack \
  --stack-name web-server-stack \
  --template-body file://web-server.yaml

# Delete a stack
aws cloudformation delete-stack \
  --stack-name web-server-stack
```

### Azure Resource Manager (ARM) Templates

ARM templates are Azure's native IaC solution for deploying and managing Azure resources.

#### Key Features

- **Declarative syntax**: JSON-based template language
- **Resource dependencies**: Automatic handling of resource dependencies
- **Repeatable deployments**: Consistent resource provisioning
- **Integration**: Deep integration with Azure services

#### Example ARM Template

```json
{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "*******",
  "parameters": {
    "vmName": {
      "type": "string",
      "defaultValue": "web-vm",
      "metadata": {
        "description": "Name of the VM"
      }
    },
    "adminUsername": {
      "type": "string",
      "metadata": {
        "description": "Admin username for the VM"
      }
    }
  },
  "resources": [
    {
      "type": "Microsoft.Compute/virtualMachines",
      "apiVersion": "2021-07-01",
      "name": "[parameters('vmName')]",
      "location": "[resourceGroup().location]",
      "properties": {
        "hardwareProfile": {
          "vmSize": "Standard_B1s"
        },
        "osProfile": {
          "computerName": "[parameters('vmName')]",
          "adminUsername": "[parameters('adminUsername')]",
          "adminPassword": "P@ssw0rd1234!"
        },
        "storageProfile": {
          "imageReference": {
            "publisher": "Canonical",
            "offer": "UbuntuServer",
            "sku": "18.04-LTS",
            "version": "latest"
          }
        }
      }
    }
  ]
}
```

### Google Cloud Deployment Manager

Deployment Manager is Google Cloud's IaC service for creating and managing Google Cloud resources.

#### Key Features

- **Template-based**: YAML, Python, or Jinja2 templates
- **Preview feature**: Preview changes before deployment
- **Automatic updates**: Update resources with configuration changes
- **Cross-project deployments**: Deploy resources across projects

#### Example Deployment Manager Configuration

```yaml
# vm-template.yaml
resources:
- name: web-vm
  type: compute.v1.instance
  properties:
    zone: us-central1-a
    machineType: zones/us-central1-a/machineTypes/f1-micro
    disks:
    - boot: true
      autoDelete: true
      initializeParams:
        sourceImage: projects/ubuntu-os-cloud/global/images/family/ubuntu-2004-lts
    networkInterfaces:
    - network: global/networks/default
      accessConfigs:
      - name: External NAT
        type: ONE_TO_ONE_NAT
```

#### Deployment Manager Commands

```bash
# Create a deployment
gcloud deployment-manager deployments create web-deployment \
  --config vm-template.yaml

# Update a deployment
gcloud deployment-manager deployments update web-deployment \
  --config vm-template.yaml
```

### Pulumi

Pulumi is a modern IaC tool that allows you to define infrastructure using familiar programming languages like Python, JavaScript, TypeScript, Go, and .NET.

#### Key Features

- **Programming languages**: Define infrastructure in real programming languages
- **Multi-cloud support**: Works with AWS, Azure, GCP, and others
- **Real code benefits**: Use loops, conditionals, functions, classes
- **Package management**: Leverage existing package managers

#### Example Pulumi Program (Python)

```python
import pulumi
import pulumi_aws as aws

# Create a VPC
vpc = aws.ec2.Vpc("main",
    cidr_block="10.0.0.0/16",
    tags={
        "Name": "Main VPC"
    })

# Create a subnet
subnet = aws.ec2.Subnet("public",
    vpc_id=vpc.id,
    cidr_block="********/24",
    tags={
        "Name": "Public Subnet"
    })

# Create an EC2 instance
web_server = aws.ec2.Instance("web",
    ami="ami-0c55b159cbfafe1d0",
    instance_type="t2.micro",
    subnet_id=subnet.id,
    tags={
        "Name": "Web Server"
    })

# Export the public IP
pulumi.export("web_server_ip", web_server.public_ip)
```

#### Pulumi Commands

```bash
# Initialize a new Pulumi project
pulumi new aws-python

# Preview infrastructure changes
pulumi preview

# Deploy infrastructure
pulumi up

# Destroy infrastructure
pulumi destroy
```

## Configuration Management Tools

Configuration management tools focus on configuring and managing existing infrastructure rather than provisioning it.

### Ansible

Ansible is an agentless automation tool that uses YAML-based playbooks to configure systems.

#### Key Features

- **Agentless**: No software required on target systems
- **SSH-based**: Uses SSH for communication
- **YAML syntax**: Easy-to-read playbooks
- **Idempotent**: Safe to run multiple times

#### Example Ansible Playbook

```yaml
# web-server.yml
---
- name: Configure web server
  hosts: webservers
  become: yes
  tasks:
    - name: Install Apache
      apt:
        name: apache2
        state: present

    - name: Start Apache service
      service:
        name: apache2
        state: started
        enabled: yes

    - name: Deploy index.html
      copy:
        content: |
          <h1>Welcome to our website!</h1>
          <p>Deployed with Ansible</p>
        dest: /var/www/html/index.html
```

#### Ansible Commands

```bash
# Run a playbook
ansible-playbook web-server.yml

# Run ad-hoc commands
ansible webservers -m ping

# Gather facts about systems
ansible webservers -m setup
```

### Chef

Chef is a configuration management tool that uses Ruby-based recipes to define system configurations.

#### Key Features

- **Chef recipes**: Ruby-based configuration definitions
- **Chef cookbooks**: Collections of related recipes
- **Chef server**: Centralized management platform
- **Test kitchen**: Testing framework for infrastructure code

#### Example Chef Recipe

```ruby
# web_server.rb
package 'apache2' do
  action :install
end

service 'apache2' do
  action [:enable, :start]
end

template '/var/www/html/index.html' do
  source 'index.html.erb'
  mode '0644'
  variables(
    title: 'Welcome to our website!',
    message: 'Deployed with Chef'
  )
end
```

### Puppet

Puppet is an infrastructure automation tool that uses a declarative language to define system configurations.

#### Key Features

- **Manifests**: Declarative configuration files
- **Modules**: Reusable collections of manifests
- **Puppet agent**: Runs on target systems
- **Puppet server**: Centralized management

#### Example Puppet Manifest

```puppet
# web_server.pp
class web_server {
  package { 'apache2':
    ensure => installed,
  }

  service { 'apache2':
    ensure => running,
    enable => true,
    require => Package['apache2'],
  }

  file { '/var/www/html/index.html':
    ensure  => file,
    content => "<h1>Welcome to our website!</h1><p>Deployed with Puppet</p>",
    require => Package['apache2'],
  }
}
```

### SaltStack

SaltStack is a remote execution and configuration management tool that uses YAML-based state files.

#### Key Features

- **Remote execution**: Execute commands on multiple systems
- **State files**: YAML-based configuration definitions
- **High-speed communication**: Fast communication with minions
- **Event-driven automation**: React to system events

#### Example Salt State File

```yaml
# web_server.sls
apache2:
  pkg.installed
  service.running:
    - enable: True
    - require:
      - pkg: apache2

/var/www/html/index.html:
  file.managed:
    - contents: |
        <h1>Welcome to our website!</h1>
        <p>Deployed with SaltStack</p>
    - require:
      - pkg: apache2
```

## Choosing the Right IaC Tool

### Factors to Consider

1. **Cloud provider preference**: Native tools vs. multi-cloud tools
2. **Team expertise**: Programming skills and learning curve
3. **Infrastructure complexity**: Simple vs. complex deployments
4. **Integration requirements**: CI/CD, monitoring, security tools
5. **Compliance needs**: Audit trails and governance requirements

### Common Tool Combinations

Many organizations use a combination of tools:

- **Terraform + Ansible**: Terraform for provisioning, Ansible for configuration
- **CloudFormation + Chef**: Native AWS provisioning with Chef configuration
- **ARM + Puppet**: Azure provisioning with Puppet configuration

## Conclusion

Understanding the core IaC tools and their capabilities is essential for implementing effective infrastructure automation. Each tool has its strengths and is suited for different use cases. The choice of tools should align with organizational requirements, team expertise, and infrastructure complexity.
