# Specialized Areas in Infrastructure as Code

As Infrastructure as Code (IaC) matures, specialized areas have emerged that focus on specific aspects of infrastructure management. This guide covers serverless infrastructure, database as code, network as code, security as code, and documentation as code.

## Serverless Infrastructure Management

Serverless computing allows you to build and run applications without thinking about servers. IaC tools enable consistent provisioning and management of serverless resources.

### AWS Lambda with Terraform

Provision and manage Lambda functions:

```hcl
# Lambda function
resource "aws_lambda_function" "api_handler" {
  filename         = "lambda_function_payload.zip"
  function_name    = "api-handler"
  role            = aws_iam_role.lambda_exec.arn
  handler         = "index.handler"
  runtime         = "nodejs18.x"
  source_code_hash = filebase64sha256("lambda_function_payload.zip")
  
  environment {
    variables = {
      ENVIRONMENT = var.environment
      LOG_LEVEL   = "info"
    }
  }
  
  tags = {
    Environment = var.environment
    Service     = "api"
  }
}

# API Gateway
resource "aws_api_gateway_rest_api" "api" {
  name        = "serverless-api"
  description = "Serverless API Gateway"
  
  endpoint_configuration {
    types = ["REGIONAL"]
  }
  
  tags = {
    Environment = var.environment
  }
}

# API Gateway Method
resource "aws_api_gateway_method" "proxy" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_rest_api.api.root_resource_id
  http_method   = "ANY"
  authorization = "NONE"
}

# API Gateway Integration
resource "aws_api_gateway_integration" "lambda" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_rest_api.api.root_resource_id
  http_method             = aws_api_gateway_method.proxy.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.api_handler.invoke_arn
}
```

### Event-Driven Architectures

Connect serverless components with events:

```hcl
# S3 trigger for Lambda
resource "aws_s3_bucket_notification" "bucket_notification" {
  bucket = aws_s3_bucket.processing.id
  
  lambda_function {
    lambda_function_arn = aws_lambda_function.process_file.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "uploads/"
    filter_suffix       = ".jpg"
  }
}

# CloudWatch Events trigger
resource "aws_cloudwatch_event_rule" "schedule" {
  name                = "daily-cleanup"
  description         = "Trigger daily cleanup function"
  schedule_expression = "rate(1 day)"
}

resource "aws_cloudwatch_event_target" "lambda" {
  rule      = aws_cloudwatch_event_rule.schedule.name
  target_id = "SendToLambda"
  arn       = aws_lambda_function.cleanup.arn
}
```

### Serverless Framework

Alternative tool specifically for serverless applications:

```yaml
# serverless.yml
service: my-service

provider:
  name: aws
  runtime: nodejs18.x
  region: us-west-2
  environment:
    ENVIRONMENT: ${env:ENVIRONMENT}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - s3:GetObject
        - s3:PutObject
      Resource: "arn:aws:s3:::my-bucket/*"

functions:
  api:
    handler: handler.api
    events:
      - http:
          path: /api
          method: get
          cors: true
  processor:
    handler: handler.process
    events:
      - s3:
          bucket: my-processing-bucket
          event: s3:ObjectCreated:*
          rules:
            - prefix: uploads/
            - suffix: .jpg

resources:
  Resources:
    ProcessingBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: my-processing-bucket
```

## Database as Code

Database as Code involves managing database schemas, configurations, and migrations through version-controlled code.

### Schema Management

Version control database schemas:

```sql
-- migrations/001_create_users_table.sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- migrations/002_add_user_profile.sql
ALTER TABLE users ADD COLUMN (
  first_name VARCHAR(50),
  last_name VARCHAR(50),
  bio TEXT
);
```

### Terraform for Database Provisioning

Provision database infrastructure:

```hcl
# RDS instance
resource "aws_db_instance" "main" {
  identifier             = "main-db-${var.environment}"
  engine                 = "postgres"
  engine_version         = "14.5"
  instance_class         = var.db_instance_class
  allocated_storage      = var.db_allocated_storage
  storage_type           = "gp2"
  storage_encrypted      = true
  kms_key_id             = aws_kms_key.rds.arn
  username               = var.db_username
  password               = var.db_password
  db_subnet_group_name   = aws_db_subnet_group.main.name
  vpc_security_group_ids = [aws_security_group.db.id]
  parameter_group_name   = aws_db_parameter_group.main.name
  
  backup_retention_period = 7
  backup_window           = "03:00-04:00"
  maintenance_window      = "sun:04:00-sun:05:00"
  
  multi_az               = var.environment == "prod" ? true : false
  skip_final_snapshot    = var.environment == "dev" ? true : false
  
  tags = {
    Environment = var.environment
    Service     = "database"
  }
}

# Database parameter group
resource "aws_db_parameter_group" "main" {
  name   = "main-db-params-${var.environment}"
  family = "postgres14"
  
  parameter {
    name  = "log_statement"
    value = "all"
  }
  
  parameter {
    name  = "log_min_duration_statement"
    value = "1000"
  }
}
```

### Database Migration Tools

Automate database schema changes:

```yaml
# dbmate configuration
# .env
DATABASE_URL=postgres://user:pass@localhost:5432/mydb

# migrations/20230101000001_create_users_table.sql
-- migrate:up
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- migrate:down
DROP TABLE users;
```

```bash
# Apply migrations
dbmate up

# Rollback migrations
dbmate down

# Create new migration
dbmate new create_posts_table
```

### Database Seeding

Automate initial data population:

```json
// seeds/users.json
[
  {
    "username": "admin",
    "email": "<EMAIL>",
    "role": "administrator"
  },
  {
    "username": "user1",
    "email": "<EMAIL>",
    "role": "user"
  }
]
```

```python
# seed.py
import json
import psycopg2

def seed_database():
    conn = psycopg2.connect(
        host="localhost",
        database="mydb",
        user="user",
        password="pass"
    )
    
    with open('seeds/users.json') as f:
        users = json.load(f)
    
    cur = conn.cursor()
    for user in users:
        cur.execute(
            "INSERT INTO users (username, email, role) VALUES (%s, %s, %s)",
            (user['username'], user['email'], user['role'])
        )
    
    conn.commit()
    cur.close()
    conn.close()

if __name__ == "__main__":
    seed_database()
```

## Network as Code

Network as Code involves managing network infrastructure and configurations through code.

### Software-Defined Networking

Define network topologies programmatically:

```hcl
# VPC and subnets
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = {
    Name        = "main-vpc"
    Environment = var.environment
  }
}

resource "aws_subnet" "public" {
  count                   = length(var.public_subnet_cidrs)
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = var.availability_zones[count.index]
  map_public_ip_on_launch = true
  
  tags = {
    Name        = "public-subnet-${count.index}"
    Environment = var.environment
  }
}

resource "aws_subnet" "private" {
  count             = length(var.private_subnet_cidrs)
  vpc_id            = aws_vpc.main.id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]
  
  tags = {
    Name        = "private-subnet-${count.index}"
    Environment = var.environment
  }
}
```

### Network Security Policies

Implement network security through code:

```hcl
# Security groups
resource "aws_security_group" "web" {
  name        = "web-sg"
  description = "Security group for web servers"
  vpc_id      = aws_vpc.main.id
  
  ingress {
    description = "HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  ingress {
    description = "HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = {
    Environment = var.environment
  }
}

# Network ACLs
resource "aws_network_acl" "main" {
  vpc_id = aws_vpc.main.id
  
  ingress {
    protocol   = "tcp"
    rule_no    = 100
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = 80
    to_port    = 80
  }
  
  ingress {
    protocol   = "tcp"
    rule_no    = 110
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = 443
    to_port    = 443
  }
  
  egress {
    protocol   = "-1"
    rule_no    = 100
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = 0
    to_port    = 0
  }
}
```

### Service Mesh Configuration

Manage microservices networking with service mesh:

```yaml
# Istio VirtualService
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: web-service
spec:
  hosts:
  - web.example.com
  gateways:
  - web-gateway
  http:
  - match:
    - uri:
        prefix: /api
    route:
    - destination:
        host: api-service
        port:
          number: 80
    retries:
      attempts: 3
      perTryTimeout: 2s
  - route:
    - destination:
        host: web-service
        port:
          number: 80
```

```yaml
# Istio DestinationRule
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: api-service
spec:
  host: api-service
  trafficPolicy:
    loadBalancer:
      simple: LEAST_CONN
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutive5xxErrors: 7
      interval: 30s
      baseEjectionTime: 30s
```

## Security as Code

Security as Code integrates security controls and compliance checks directly into infrastructure code.

### Infrastructure Security Policies

Define security policies as code:

```hcl
# AWS Config rules
resource "aws_config_config_rule" "required_tags" {
  name = "required-tags"
  
  source {
    owner             = "AWS"
    source_identifier = "REQUIRED_TAGS"
  }
  
  input_parameters = jsonencode({
    tag1Key = "Environment"
    tag2Key = "Owner"
  })
}

resource "aws_config_config_rule" "encrypted_volumes" {
  name = "encrypted-volumes"
  
  source {
    owner             = "AWS"
    source_identifier = "ENCRYPTED_VOLUMES"
  }
}
```

### Secrets Management

Securely manage secrets through code:

```hcl
# AWS Secrets Manager
resource "aws_secretsmanager_secret" "database_credentials" {
  name = "${var.environment}/database/credentials"
  
  tags = {
    Environment = var.environment
  }
}

resource "aws_secretsmanager_secret_version" "database_credentials" {
  secret_id     = aws_secretsmanager_secret.database_credentials.id
  secret_string = jsonencode({
    username = var.db_username
    password = var.db_password
  })
}

# Rotate secrets
resource "aws_secretsmanager_secret_rotation" "database_credentials" {
  secret_id           = aws_secretsmanager_secret.database_credentials.id
  rotation_lambda_arn = aws_lambda_function.rotate_secrets.arn
  
  rotation_rules {
    automatically_after_days = 30
  }
}
```

### Compliance Automation

Automate compliance checks:

```python
# compliance-check.py
import boto3
import json

def check_compliance():
    ec2 = boto3.client('ec2')
    s3 = boto3.client('s3')
    
    # Check if all S3 buckets are encrypted
    buckets = s3.list_buckets()
    for bucket in buckets['Buckets']:
        try:
            encryption = s3.get_bucket_encryption(Bucket=bucket['Name'])
            print(f"Bucket {bucket['Name']} is encrypted")
        except:
            print(f"Bucket {bucket['Name']} is NOT encrypted")
            # Remediate by enabling encryption
            s3.put_bucket_encryption(
                Bucket=bucket['Name'],
                ServerSideEncryptionConfiguration={
                    'Rules': [
                        {
                            'ApplyServerSideEncryptionByDefault': {
                                'SSEAlgorithm': 'AES256'
                            }
                        }
                    ]
                }
            )
    
    # Check if all EC2 instances have termination protection
    instances = ec2.describe_instances()
    for reservation in instances['Reservations']:
        for instance in reservation['Instances']:
            if not instance.get('DisableApiTermination', {}).get('Value', False):
                print(f"Instance {instance['InstanceId']} lacks termination protection")
                # Remediate by enabling termination protection
                ec2.modify_instance_attribute(
                    InstanceId=instance['InstanceId'],
                    DisableApiTermination={'Value': True}
                )

if __name__ == "__main__":
    check_compliance()
```

## Documentation as Code

Documentation as Code treats documentation like code, versioning it alongside infrastructure definitions.

### Infrastructure Documentation

Generate documentation from infrastructure code:

```hcl
# infrastructure.md template
locals {
  infrastructure_docs = <<EOT
# Infrastructure Documentation

## Overview

This document describes the ${var.environment} environment infrastructure.

## Network

- VPC CIDR: ${var.vpc_cidr}
- Public Subnets: ${join(", ", var.public_subnet_cidrs)}
- Private Subnets: ${join(", ", var.private_subnet_cidrs)}

## Compute

- Instance Type: ${var.instance_type}
- Auto Scaling: Min ${var.asg_min}, Max ${var.asg_max}

## Database

- Engine: ${var.db_engine}
- Version: ${var.db_version}
- Instance Class: ${var.db_instance_class}
EOT
}

resource "local_file" "infrastructure_docs" {
  content  = local.infrastructure_docs
  filename = "${path.module}/docs/infrastructure.md"
}
```

### Architecture Diagrams as Code

Generate architecture diagrams from code:

```python
# diagram.py
from diagrams import Diagram, Cluster
from diagrams.aws.compute import EC2, AutoScaling
from diagrams.aws.network import ELB, VPC, InternetGateway
from diagrams.aws.database import RDS
from diagrams.aws.storage import S3

with Diagram("Web Service Architecture", show=False):
    with Cluster("VPC"):
        igw = InternetGateway("Internet Gateway")
        
        with Cluster("Public Subnet"):
            lb = ELB("Load Balancer")
            
        with Cluster("Private Subnet"):
            asg = AutoScaling("Auto Scaling Group")
            ec2 = [EC2("Web Server 1"),
                   EC2("Web Server 2")]
            
        with Cluster("Database Subnet"):
            rds = RDS("Database")
            
        s3 = S3("Static Assets")
        
    igw >> lb >> asg
    asg >> ec2 >> rds
    ec2 >> s3
```

### API Documentation

Generate API documentation from code:

```yaml
# openapi.yaml
openapi: 3.0.0
info:
  title: User API
  version: 1.0.0
  description: API for managing users

servers:
  - url: https://api.example.com/v1
    description: Production server

paths:
  /users:
    get:
      summary: List all users
      description: Returns a list of all users
      responses:
        '200':
          description: A list of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
    post:
      summary: Create a user
      description: Creates a new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '201':
          description: User created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          description: User ID
        username:
          type: string
          description: Username
        email:
          type: string
          description: Email address
      required:
        - username
        - email
```

### Automated Documentation Generation

Automate documentation updates:

```bash
#!/bin/bash
# generate-docs.sh

# Generate infrastructure documentation
terraform-docs markdown . > docs/infrastructure.md

# Generate architecture diagrams
python diagram.py

# Generate API documentation
npx redoc-cli build openapi.yaml -o docs/api.html

# Update documentation repository
git add docs/
git commit -m "Update documentation"
git push origin main
```

## Integration Patterns

### Combining Specialized Areas

Integrate multiple specialized areas:

```hcl
# Complete serverless application with all specialized areas
module "serverless_app" {
  source = "./modules/serverless-app"
  
  # Serverless infrastructure
  function_name   = "user-api"
  runtime         = "nodejs18.x"
  handler         = "index.handler"
  
  # Database as code
  db_name         = "users"
  db_engine       = "postgres"
  db_version      = "14.5"
  
  # Network as code
  vpc_id          = module.network.vpc_id
  subnet_ids      = module.network.private_subnet_ids
  
  # Security as code
  kms_key_id      = module.security.kms_key_id
  secrets_arn     = module.security.secrets_arn
  
  # Documentation as code
  documentation   = true
  diagram         = true
}
```

### CI/CD for Specialized Areas

Implement CI/CD pipelines for specialized areas:

```yaml
# .github/workflows/specialized-areas.yaml
name: Specialized Areas CI/CD

on:
  push:
    branches:
      - main
    paths:
      - 'database/**'
      - 'network/**'
      - 'security/**'

jobs:
  database:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Validate database schema
        run: |
          cd database
          # Run schema validation
          sqllint migrations/
      
      - name: Test migrations
        run: |
          # Set up test database
          docker run -d --name test-db -e POSTGRES_PASSWORD=test postgres
          # Apply migrations
          # Run tests
      
      - name: Deploy migrations
        run: |
          # Apply migrations to production
          # dbmate up
  
  network:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Terraform Network Plan
        run: |
          cd network
          terraform init
          terraform plan
      
      - name: Validate Network Policies
        run: |
          # Validate network policies
          # kubectl apply --dry-run -f network-policies/
  
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Security Scan
        run: |
          # Run security scans
          checkov -d .
          tfsec .
      
      - name: Compliance Check
        run: |
          # Run compliance checks
          python compliance-check.py
  
  documentation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Generate Documentation
        run: |
          # Generate documentation
          ./generate-docs.sh
      
      - name: Deploy Documentation
        run: |
          # Deploy to documentation site
          # cd docs && npm run deploy
```

## Conclusion

Specialized areas in Infrastructure as Code enable organizations to manage specific aspects of their infrastructure with greater precision and automation. By treating serverless infrastructure, databases, networks, security, and documentation as code, teams can achieve higher levels of consistency, compliance, and operational efficiency. These specialized approaches complement core IaC practices and allow for deeper automation and governance across all infrastructure domains. As these practices mature, they become essential components of a comprehensive IaC strategy that covers all aspects of modern infrastructure management.
