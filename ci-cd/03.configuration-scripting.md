# Configuration and Scripting for CI/CD

Effective CI/CD pipelines require strong configuration and scripting skills. This guide covers the essential technologies and practices used in modern CI/CD environments.

## YAML Syntax

YAML (YAML Ain't Markup Language) is a human-readable data serialization standard used by most modern CI/CD platforms.

### Basic YAML Elements

#### Key-Value Pairs

```yaml
name: <PERSON>
age: 30
active: true
```

#### Lists/Arrays

```yaml
# Inline format
fruits: [apple, banana, orange]

# Multi-line format
fruits:
  - apple
  - banana
  - orange
```

#### Nested Objects

```yaml
person:
  name: <PERSON>
  age: 30
  address:
    street: 123 Main St
    city: Anytown
    state: CA
```

#### Comments and Anchors

```yaml
# This is a comment

# Anchors for reusing values
defaults: &defaults
  environment: production
  timeout: 300

job1:
  <<: *defaults
  name: job-one
  
job2:
  <<: *defaults
  name: job-two
```

### YAML Best Practices

1. **Consistent Indentation**: Use spaces (typically 2) for indentation
2. **Avoid Tabs**: Tabs can cause parsing errors
3. **Quotes**: Use quotes for strings with special characters
4. **Comments**: Use comments to explain complex configurations

### Common YAML Patterns in CI/CD

```yaml
# Pipeline definition
pipeline:
  stages:
    - name: build
      steps:
        - name: compile
          command: make build
        - name: test
          command: make test
          
# Environment variables
environment:
  variables:
    NODE_ENV: production
    DATABASE_URL: ${DB_CONNECTION_STRING}
    
# Conditional execution
jobs:
  deploy:
    only:
      branches:
        - main
        - release/*
    except:
      branches:
        - develop
```

## Shell Scripting

Shell scripting is fundamental for automation tasks in CI/CD pipelines.

### Bash Basics

#### Variables

```bash
# Variable assignment
NAME="John"
AGE=30

# Using variables
echo "Hello, $NAME"
echo "You are $AGE years old"

# Command substitution
DATE=$(date)
FILES=$(ls -la)
```

#### Conditionals

```bash
# If statement
if [ $AGE -gt 18 ]; then
  echo "You are an adult"
elif [ $AGE -eq 18 ]; then
  echo "You just became an adult"
else
  echo "You are a minor"
fi

# Case statement
case $NAME in
  "John")
    echo "Hello John"
    ;;
  "Jane")
    echo "Hello Jane"
    ;;
  *)
    echo "Hello stranger"
    ;;
esac
```

#### Loops

```bash
# For loop
for i in {1..5}; do
  echo "Iteration $i"
done

# While loop
COUNT=0
while [ $COUNT -lt 5 ]; do
  echo "Count: $COUNT"
  ((COUNT++))
done

# Loop through files
for file in *.txt; do
  echo "Processing $file"
done
```

#### Functions

```bash
# Function definition
greet() {
  local name=$1
  echo "Hello, $name!"
}

# Function with return value
calculate() {
  local a=$1
  local b=$2
  echo $(($a + $b))
}

# Calling functions
greet "John"
result=$(calculate 5 3)
echo "Result: $result"
```

### PowerShell Basics

PowerShell is Microsoft's task automation framework, commonly used in Windows environments.

#### Variables and Data Types

```powershell
# Variable assignment
$name = "John"
$age = 30
$isActive = $true

# Arrays
$fruits = @("apple", "banana", "orange")

# Hash tables
$person = @{
  Name = "John"
  Age = 30
  City = "New York"
}
```

#### Conditionals and Loops

```powershell
# If statement
if ($age -gt 18) {
  Write-Host "You are an adult"
} elseif ($age -eq 18) {
  Write-Host "You just became an adult"
} else {
  Write-Host "You are a minor"
}

# For loop
for ($i = 1; $i -le 5; $i++) {
  Write-Host "Iteration $i"
}

# Foreach loop
$numbers = 1..5
foreach ($number in $numbers) {
  Write-Host "Number: $number"
}
```

#### Cmdlets and Commands

```powershell
# File operations
Get-ChildItem -Path C:\Temp
New-Item -ItemType Directory -Name "NewFolder"
Copy-Item -Path "source.txt" -Destination "destination.txt"

# Process management
Get-Process | Where-Object {$_.CPU -gt 100}
Stop-Process -Name "notepad"

# Service management
Get-Service | Where-Object {$_.Status -eq "Running"}
Restart-Service -Name "Spooler"
```

## Dockerfile and Containerization

Dockerfiles define how to build Docker images, which are essential for consistent deployments.

### Dockerfile Instructions

```dockerfile
# Base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Expose port
EXPOSE 3000

# Environment variables
ENV NODE_ENV=production

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

# Start command
CMD ["npm", "start"]
```

### Multi-stage Builds

```dockerfile
# Build stage
FROM node:18 AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage
FROM node:18-alpine AS production
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY --from=builder /app/dist ./dist
EXPOSE 3000
CMD ["npm", "start"]
```

### Docker Best Practices

1. **Use Specific Tags**: Avoid using `latest` tag
2. **Minimize Layers**: Combine related commands
3. **Multi-stage Builds**: Reduce final image size
4. **Non-root User**: Improve security
5. **Health Checks**: Monitor container status

## Infrastructure as Code (IaC) Fundamentals

IaC manages infrastructure through machine-readable definition files rather than physical hardware configuration.

### IaC Benefits

- **Consistency**: Identical environments across deployments
- **Version Control**: Track infrastructure changes
- **Automation**: Reduce manual provisioning errors
- **Speed**: Rapid environment provisioning
- **Documentation**: Infrastructure as self-documenting code

### Popular IaC Tools

#### Terraform

```hcl
# AWS EC2 instance
provider "aws" {
  region = "us-west-2"
}

resource "aws_instance" "web" {
  ami           = "ami-0c55b159cbfafe1d0"
  instance_type = "t3.micro"
  
  tags = {
    Name = "WebServer"
  }
}

# Output values
output "instance_ip" {
  value = aws_instance.web.public_ip
}
```

#### Ansible

```yaml
# Playbook example
---
- name: Configure web servers
  hosts: webservers
  become: yes
  
  tasks:
    - name: Install nginx
      apt:
        name: nginx
        state: present
        
    - name: Start nginx service
      service:
        name: nginx
        state: started
        enabled: yes
        
    - name: Deploy configuration
      template:
        src: nginx.conf.j2
        dest: /etc/nginx/nginx.conf
      notify: restart nginx
      
  handlers:
    - name: restart nginx
      service:
        name: nginx
        state: restarted
```

#### CloudFormation

```yaml
# AWS CloudFormation template
AWSTemplateFormatVersion: '2010-09-09'
Description: Simple EC2 instance

Parameters:
  InstanceType:
    Type: String
    Default: t3.micro
    AllowedValues:
      - t3.micro
      - t3.small
    
Resources:
  WebServer:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: ami-0c55b159cbfafe1d0
      InstanceType: !Ref InstanceType
      Tags:
        - Key: Name
          Value: WebServer
          
Outputs:
  InstanceId:
    Description: Instance ID
    Value: !Ref WebServer
    
  PublicIP:
    Description: Public IP address
    Value: !GetAtt WebServer.PublicIp
```

### IaC Best Practices

1. **Modular Design**: Break into reusable components
2. **Version Control**: Store definitions in Git
3. **Testing**: Validate configurations before deployment
4. **Documentation**: Comment complex configurations
5. **Security**: Scan for vulnerabilities and misconfigurations

Mastering these configuration and scripting skills is essential for building robust, maintainable CI/CD pipelines.