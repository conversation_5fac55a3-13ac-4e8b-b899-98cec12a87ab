# Networking

## Amazon VPC (Virtual Private Cloud)

### What is Amazon VPC?

Amazon VPC lets you provision a logically isolated section of the AWS Cloud where you can launch AWS resources in a virtual network that you define.

### VPC Components

#### Subnets
- Logical subdivision of VPC
- Associated with specific AZ
- Can be public or private
- CIDR block must be subset of VPC CIDR

#### Route Tables
- Determine where network traffic is directed
- Associated with subnets
- Contains routing rules (routes)
- Default route table created with VPC

#### Internet Gateway
- Enables communication between VPC and internet
- Highly available and redundant
- One per VPC
- Required for public subnets

#### NAT Gateway
- Enables instances in private subnets to connect to internet
- Prevents internet from initiating connections to private instances
- Managed service (highly available)
- Charged hourly and per GB data processed

#### Security Groups
- Virtual firewall at instance level
- Stateful (return traffic automatically allowed)
- Supports allow rules only
- Associated with network interfaces

#### Network Access Control Lists (NACLs)
- Virtual firewall at subnet level
- Stateless (return traffic must be explicitly allowed)
- Supports allow and deny rules
- Default NACL created with VPC

### VPC Design Considerations

#### CIDR Block Planning
- VPC CIDR: /16 to /28
- Subnet CIDR: /16 to /28
- Avoid overlapping with on-premises networks
- Plan for future growth

#### High Availability
- Deploy across multiple AZs
- Distribute subnets across AZs
- Use multiple NAT gateways

#### Security
- Principle of least privilege
- Defense in depth
- Regular security reviews

### VPC Peering

#### What is VPC Peering?
- Networking connection between two VPCs
- Enables routing of traffic using private IP addresses
- Instances behave as if on same network

#### VPC Peering Limitations
- No transitive peering
- No overlapping CIDR blocks
- Within same region for basic peering

### VPC Endpoints

#### Gateway Endpoints
- S3 and DynamoDB only
- Highly available by default
- No additional charges
- Policy-based access control

#### Interface Endpoints
- Private connectivity to AWS services
- Uses AWS PrivateLink
- Elastic network interfaces in subnets
- Charged hourly and per GB data processed

## Amazon Route 53

### What is Route 53?

Amazon Route 53 is a highly available and scalable Domain Name System (DNS) web service.

### Route 53 Routing Policies

#### Simple Routing
- Single resource performs function
- No health checks
- Returns all IP addresses

#### Failover Routing
- Active-passive setup
- Health checks determine primary/secondary
- Automatic failover

#### Geolocation Routing
- Route based on user location
- Can specify default record
- Useful for content localization

#### Geoproximity Routing
- Route based on location of resources
- Can shift traffic with bias
- Requires Route 53 Traffic Flow

#### Latency Routing
- Route to lowest latency region
- Based on traffic between users and AWS regions

#### Weighted Routing
- Split traffic based on weights
- Useful for testing new versions
- Traffic shaping

#### Multivalue Answer Routing
- Return multiple healthy records
- Similar to simple with health checks
- Up to 8 healthy records

### Route 53 Features

#### DNS Failover
- Health checks for endpoints
- CloudWatch metrics
- Automated DNS failover

#### Domain Registration
- Register domain names
- Manage domain settings
- Transfer domains

#### Traffic Flow
- Visual editor for routing policies
- Version control for routing policies
- Simplified management

## AWS Direct Connect

### What is AWS Direct Connect?

AWS Direct Connect is a cloud service solution that makes it easy to establish a dedicated network connection from your premises to AWS.

### Direct Connect Benefits

- Consistent network experience
- Reduced bandwidth costs
- More consistent network performance
- Compatible with all AWS services

### Direct Connect Components

#### Direct Connect Locations
- AWS or partner network locations
- Where connections are established

#### Connections
- Physical Ethernet connections
- 1 Gbps or 10 Gbps
- Dedicated or hosted

#### Virtual Interfaces
- VLANs on connections
- Public or private
- Associate with VPC or AWS services

### Direct Connect Gateway

- Connect to VPCs in different regions
- Single gateway for multiple VPCs
- Simplified management

## AWS VPN

### AWS Site-to-Site VPN

#### What is Site-to-Site VPN?
- Secure connection between on-premises and VPC
- Encrypted IPSec tunnels
- Redundant tunnels

#### VPN Components

##### Customer Gateway
- Software application or physical device
- On customer side of VPN connection
- Specify public IP and device type

##### Virtual Private Gateway
- VPN concentrator on AWS side
- Attached to VPC
- Handles VPN termination

##### VPN Connection
- Connection between customer gateway and virtual private gateway
- Two tunnels for redundancy
- Automatically established

### AWS Client VPN

#### What is Client VPN?
- Secure connection for individual devices
- Connect to AWS or on-premises networks
- Managed service

#### Client VPN Features
- Authentication integration
- Authorization rules
- Client connection logging
- Active Directory integration

## Elastic Load Balancing

### What is Elastic Load Balancing?

Elastic Load Balancing automatically distributes incoming application traffic across multiple targets.

### Load Balancer Types

#### Application Load Balancer (ALB)
- Operates at Layer 7 (Application Layer)
- HTTP/HTTPS traffic
- Advanced routing (path, host, headers)
- Microservices and containers

#### Network Load Balancer (NLB)
- Operates at Layer 4 (Transport Layer)
- TCP/UDP traffic
- Ultra-high performance
- Static IP support

#### Gateway Load Balancer (GLB)
- Deploy, scale, and manage virtual appliances
- GENEVE protocol
- Traffic inspection and security

### Load Balancer Features

#### Health Checks
- Monitor target health
- Automatic registration/deregistration
- Customizable thresholds

#### Security
- SSL termination
- Security groups
- Access logs

#### Integration
- Auto Scaling groups
- CloudWatch metrics
- Access logs

## Amazon CloudFront
n### What is CloudFront?

Amazon CloudFront is a fast content delivery network (CDN) service that securely delivers data, videos, applications, and APIs to customers globally with low latency.

### CloudFront Components

#### Edge Locations
- Endpoints for CloudFront
- Cache content for performance
- Located worldwide

#### Origin
- Source of content
- S3 bucket, EC2 instance, ELB, or custom origin

#### Distribution
- Collection of settings
- Web or RTMP distribution
- Associated with domain name

### CloudFront Features

#### Performance
- Global edge network
- Dynamic and static content
- Origin shield

#### Security
- AWS Shield integration
- Custom SSL certificates
- Field-level encryption

#### Customization
- Lambda@Edge functions
- Custom error pages
- Response headers policy

## AWS Global Accelerator
n### What is Global Accelerator?

AWS Global Accelerator is a service that improves the availability and performance of your applications with local or global users.

### Global Accelerator Benefits

- Static IP addresses
- Health check monitoring
- Traffic dial for testing
- Improved availability

### Global Accelerator Components

#### Static IP Addresses
- Two anycast IP addresses
- Never change
- Used for application endpoints

#### Accelerator
- Resource containing static IPs
- Associated with DNS name

#### Listener
- Processes inbound connections
- Port and protocol configuration

#### Endpoint Group
- Collection of endpoints in region
- Associated with listener

#### Endpoint
- Resource handling traffic
- EC2 instances, ALBs, NLBs, EIPs

## AWS Transit Gateway
n### What is Transit Gateway?

AWS Transit Gateway is a service that enables customers to connect their Amazon Virtual Private Clouds (VPCs) and their on-premises networks to a single gateway.

### Transit Gateway Benefits

- Simplified network architecture
- Transitive routing
- Centralized management
- Scalable

### Transit Gateway Components

#### Transit Gateway
- Central hub for connections
- Shared across accounts/regions

#### Transit Gateway Attachments
- Connections to resources
- VPC, VPN, Direct Connect

#### Transit Gateway Route Tables
- Control routing behavior
- Propagate and associate routes

#### Resource Access Manager (RAM)
- Share resources across accounts
- Share transit gateways
