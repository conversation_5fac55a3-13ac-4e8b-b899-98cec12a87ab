# Security

## AWS Shared Responsibility Model

### Understanding the Model

The AWS shared responsibility model defines the security responsibilities between AWS and the customer:

#### AWS Responsibility (Security OF the Cloud)
- Infrastructure protection
- Physical security of data centers
- Hardware and software infrastructure
- Network infrastructure
- Virtualization infrastructure

#### Customer Responsibility (Security IN the Cloud)
- Data protection
- Identity and access management
- Operating system configuration
- Network configuration
- Application security

### Shared Controls
- Patch management
- Configuration management
- Awareness and training

## AWS Security Services

### AWS Identity and Access Management (IAM)

#### Key Features
- Centralized control of AWS account
- Shared access to AWS account resources
- Granular permissions
- Secure access to AWS resources for applications
- Multi-factor authentication
- Temporary security credentials
- Password rotation policies

#### IAM Best Practices
- Lock away root user access keys
- Create individual IAM users
- Use groups to assign permissions
- Apply least privilege principle
- Use roles for applications
- Use policy conditions for extra security
- Monitor activity in accounts

### AWS Organizations

#### What is AWS Organizations?
- Centralized management of multiple AWS accounts
- Consolidated billing
- Hierarchical grouping of accounts

#### Key Features
- Organizational units (OUs)
- Service control policies (SCPs)
- Consolidated billing
- Integration with IAM

#### Service Control Policies (SCPs)
- Whitelist or blacklist IAM actions
- Applied to OUs or accounts
- Cannot grant permissions
- Inherit from parent entities

### AWS Key Management Service (KMS)

#### What is AWS KMS?
- Managed service for creating and controlling encryption keys
- Integrated with many AWS services
- FIPS 140-2 validated

#### KMS Key Types
1. **AWS Managed Keys**
   - Created and managed by AWS
   - Free of charge
   - Service-specific

2. **Customer Managed Keys**
   - Created and managed by customer
   - Charged per API call
   - Custom key policies

3. **AWS Owned Keys**
   - Owned and managed by AWS
   - No direct control
   - No additional charge

#### KMS Features
- Envelope encryption
- Key rotation
- Key policies
- Auditing with CloudTrail

### AWS CloudHSM

#### What is AWS CloudHSM?
- Managed hardware security module
- FIPS 140-2 Level 3 validated
- Generate and use keys in HSM

#### CloudHSM vs KMS
- CloudHSM: Full control over keys
- KMS: Managed service with integration

### AWS Certificate Manager (ACM)

#### What is ACM?
- Provision, manage, and deploy SSL/TLS certificates
- Free public certificates
- Automatic renewal

#### ACM Features
- Public and private certificates
- Integration with AWS services
- Domain validation

## AWS Security Compliance

### Compliance Programs
- SOC 1, 2, and 3
- PCI DSS Level 1
- ISO 9001, 27001, 27017, 27018
- FedRAMP
- HIPAA
- GDPR

### AWS Artifact

#### What is AWS Artifact?
- Self-service portal for compliance reports
- On-demand access to AWS security and compliance documents

#### Key Features
- Download agreements
- Access compliance reports
- Accept agreements

### AWS Config

#### What is AWS Config?
- Service for assessing, auditing, and evaluating AWS resource configurations
- Configuration history
- Configuration change notifications

#### AWS Config Rules
- Pre-built rules
- Custom rules
- Compliance evaluation
- Remediation actions

## AWS Security Monitoring

### Amazon CloudWatch

#### CloudWatch Metrics
- Collect and track metrics
- Set alarms
- Automatically react to changes

#### CloudWatch Logs
- Monitor and troubleshoot systems
- Centralized log management
- Real-time analysis

#### CloudWatch Events
- Respond to state changes
- Schedule automated actions
- Event-driven computing

### AWS CloudTrail

#### What is CloudTrail?
- Service for governance, compliance, and operational auditing
- Records AWS API calls
- History of events and actions

#### CloudTrail Features
- Event history
- Trail configuration
- Integration with CloudWatch Logs
- Log file integrity validation

### Amazon GuardDuty

#### What is GuardDuty?
- Intelligent threat detection service
- Analyzes CloudTrail, VPC Flow Logs, and DNS logs
- Machine learning and threat intelligence

#### GuardDuty Features
- Continuous monitoring
- Threat intelligence
- Machine learning
- Integration with SIEM tools

### Amazon Inspector

#### What is Amazon Inspector?
- Automated security assessment service
- Helps improve security and compliance
- Agent-based and agentless assessments

#### Inspector Assessments
- Network assessments
- Host assessments
- Security best practices
- CVE assessments

### AWS Security Hub
n#### What is Security Hub?
- Centralized view of security alerts
- Aggregates findings from multiple services
- Compliance standards

#### Security Hub Features
- Integrated dashboards
- Automated compliance checks
- Findings aggregation
- Remediation guidance

## AWS Data Protection

### Encryption Options

#### Encryption in Transit
- SSL/TLS
- IPSec VPN
- Client-side encryption

#### Encryption at Rest
- Server-side encryption
- Client-side encryption
- AWS KMS integration

### Data Classification
- Categorize data by sensitivity
- Apply appropriate controls
- Regular review and updates

### Data Loss Prevention (DLP)
- Identify sensitive data
- Monitor data movement
- Prevent unauthorized access

## AWS Incident Response

### Incident Response Plan
- Preparation
- Detection and analysis
- Containment, eradication, and recovery
- Post-incident activity

### Forensic Analysis
- Preserve evidence
- Document findings
- Maintain chain of custody

### Communication Plan
- Internal communication
- External communication
- Legal and regulatory requirements

## AWS Security Automation

### Infrastructure as Code (IaC)
- AWS CloudFormation
- HashiCorp Terraform
- Version control for infrastructure

### Security as Code
- Automated security testing
- Policy as code
- Continuous compliance

### Automated Remediation
- AWS Lambda for automated responses
- CloudWatch Events triggers
- Security automation playbooks

## AWS Penetration Testing

### Penetration Testing Rules
- Authorization required for certain services
- Prohibited activities
- Reporting vulnerabilities

### Permitted Services
- EC2 instances
- RDS instances
- CloudFront distributions
- API Gateway
- Lambda functions

### Prohibited Activities
- DNS zone walking
- Denial of service attacks
- Port scanning
- Network flooding

## AWS Security Best Practices

### Identity and Access Management
- Enable MFA for privileged users
- Use temporary credentials
- Rotate credentials regularly
- Remove unnecessary credentials

### Network Security
- Implement principle of least privilege
- Use VPC flow logs
- Regular security group reviews
- Network segmentation

### Data Protection
- Encrypt sensitive data
- Regular backup testing
- Data classification
- Secure data disposal

### Monitoring and Logging
- Enable CloudTrail in all regions
- Configure CloudWatch alarms
- Centralized log management
- Regular log review

### Incident Response
- Develop incident response plan
- Regular testing and updating
- Training for personnel
- Communication protocols
