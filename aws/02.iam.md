# Identity and Access Management (IAM)

## What is IAM?

AWS Identity and Access Management (IAM) is a web service that helps you securely control access to AWS resources. With IAM, you can centrally manage users, groups, roles, and permissions for accessing AWS services and resources.

## Key Concepts

### Users

- An entity that interacts with AWS services and resources
- Can be a human user or an application
- Each user has a unique name and ARN (Amazon Resource Name)
- Users can have long-term credentials (password, access keys)

### Groups

- Collections of IAM users
- All users in a group inherit the permissions assigned to the group
- A user can belong to multiple groups
- Groups cannot be nested (groups cannot contain other groups)

### Roles

- Entities that define a set of permissions
- Used to delegate access to AWS resources
- Can be assumed by users, applications, or AWS services
- Provide temporary security credentials

### Policies

- Documents that define permissions
- Written in JSON format
- Can be attached to users, groups, or roles
- Two types: AWS managed and customer managed

## IAM Policy Structure

### Policy Elements

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowS3BucketAccess",
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject"
      ],
      "Resource": "arn:aws:s3:::my-bucket/*"
    }
  ]
}
```

### Policy Components

- **Version**: Policy language version
- **Statement**: One or more individual statements
- **Sid**: Statement ID (optional)
- **Effect**: Allow or Deny
- **Action**: List of actions
- **Resource**: List of resources
- **Condition**: Conditions for when the policy is in effect (optional)

## IAM Best Practices

### Security

1. **Lock away root user access keys**
   - Delete root access keys if not needed
   - Create IAM users for daily tasks

2. **Create individual IAM users**
   - Don't share credentials
   - Create separate users for each person

3. **Use groups to assign permissions**
   - Assign permissions to groups, not individual users
   - Add users to appropriate groups

4. **Apply least privilege principle**
   - Grant minimum permissions required
   - Review and update permissions regularly

5. **Use roles for applications**
   - Applications running on EC2 should use roles
   - Avoid storing access keys in code

6. **Use policy conditions for extra security**
   - Restrict access based on time, IP, etc.
   - Use MFA for sensitive operations

### Password Policies

- Minimum password length
- Require specific character types
- Allow users to change their own passwords
- Require password expiration
- Prevent password reuse
- Block compromised credentials

## IAM Access Types

### Programmatic Access

- Access keys (Access Key ID and Secret Access Key)
- Used for API, CLI, and SDK access
- Should be rotated regularly

### Console Access

- Username and password
- Used for AWS Management Console access
- Can enable MFA for additional security

## Multi-Factor Authentication (MFA)

### Types of MFA

1. **Virtual MFA**
   - Software-based authenticator app
   - Examples: Google Authenticator, Authy

2. **Hardware MFA**
   - Physical device that generates codes
   - Examples: YubiKey, RSA SecurID

3. **SMS-based MFA**
   - Codes sent via SMS text message
   - Not recommended for root accounts

### MFA Best Practices

- Enable MFA for root account
- Enable MFA for privileged users
- Use hardware MFA for root account
- Regularly review MFA device assignments

## IAM Roles for AWS Services

### Common Service Roles

- **EC2 Role**: Allows EC2 instances to access other AWS services
- **Lambda Role**: Allows Lambda functions to access other AWS services
- **CloudFormation Role**: Allows CloudFormation to create resources

### Role Trust Policies

- Define who can assume the role
- Specify trusted entities (services, users, accounts)

### Role Permissions Policies

- Define what actions the role can perform
- Attached to the role as managed or inline policies

## IAM Identity Federation

### Identity Providers

- **SAML 2.0**: For integrating with corporate directories
- **OIDC**: For integrating with web identity providers
- **Custom Identity Broker**: For custom authentication solutions

### Web Identity Federation

- Use identity providers like Google, Facebook, Amazon
- AWS Security Token Service (STS) creates temporary credentials

## IAM Access Analyzer

- Identifies resources shared with external entities
- Helps identify unintended access
- Continuously monitors permissions

## IAM Security Tools

### Credential Report

- Lists all users and credential status
- Shows when passwords/access keys were last used
- Helps identify unused or old credentials

### Access Advisor

- Shows service permissions granted to users/roles
- Displays when services were last accessed
- Helps remove unnecessary permissions

### Access Analyzer

- Identifies resources shared with external entities
- Helps identify unintended access
- Continuously monitors permissions
