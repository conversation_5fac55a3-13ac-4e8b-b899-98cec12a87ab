# Storage Services

## Amazon S3 (Simple Storage Service)

### What is Amazon S3?

Amazon S3 is object storage built to store and retrieve any amount of data from anywhere on the web. It provides industry-leading durability, availability, security, and performance.

### S3 Storage Classes

1. **S3 Standard**
   - Default storage class
   - High durability and availability
   - Suitable for frequently accessed data

2. **S3 Intelligent-Tiering**
   - Automatically moves objects between access tiers
   - Optimizes costs based on access patterns
   - No retrieval fees

3. **S3 Standard-IA** (Infrequent Access)
   - Lower storage cost than S3 Standard
   - Higher retrieval costs
   - Suitable for long-lived, infrequently accessed data

4. **S3 One Zone-IA**
   - Stores data in a single AZ
   - Lower cost than S3 Standard-IA
   - Less resilient to AZ failure

5. **S3 Glacier**
   - Secure, durable, low-cost storage
   - Retrieval times in minutes to hours
   - Suitable for archival data

6. **S3 Glacier Deep Archive**
   - Lowest cost storage class
   - Retrieval time of 12 hours
   - Suitable for long-term archival

### S3 Features

#### Durability and Availability
- 99.999999999% (11 9's) durability
- 99.99% availability for S3 Standard

#### Security
- Encryption at rest and in transit
- Access control through ACLs and bucket policies
- Integration with IAM
- Server-side encryption options

#### Data Management
- Versioning
- Lifecycle policies
- Cross-region replication
- Event notifications

### S3 Bucket Configuration

#### Bucket Properties
- Bucket name (globally unique)
- Region (cannot be changed after creation)
- Versioning
- Server access logging
- Static website hosting

#### Bucket Policies
- JSON-based policies
- Control access to buckets and objects
- Can grant or deny permissions

#### Access Control Lists (ACLs)
- Control access to buckets and objects
- Legacy access control mechanism
- Being replaced by bucket policies

### S3 Performance Optimization

#### Prefixes and Partitions
- S3 automatically partitions based on object key
- Distribute objects across prefixes for better performance

#### Transfer Acceleration
- Uses CloudFront edge locations
- Faster uploads to S3
- Particularly beneficial for cross-continent transfers

#### Multi-part Upload
- Upload large objects in parts
- Improves resilience and performance
- Recommended for objects larger than 100 MB

#### Byte-Range Fetches
- Retrieve partial objects
- Parallelize downloads
- Improve resilience

## Amazon EBS (Elastic Block Store)

### What is Amazon EBS?

Amazon EBS provides block-level storage volumes for use with EC2 instances. EBS volumes behave like raw, unformatted block devices and can be mounted as devices on your instances.

### EBS Volume Types

#### SSD-backed Volumes

1. **General Purpose SSD (gp3)**
   - Latest generation SSD volume
   - Baseline of 3,000 IOPS and 125 MiB/s
   - Up to 16,000 IOPS and 1,000 MiB/s
   - Cost-effective for most workloads

2. **Provisioned IOPS SSD (io2/io1)**
   - Highest performance SSD volume
   - Up to 64,000 IOPS and 1,000 MiB/s
   - Designed for I/O-intensive applications

#### HDD-backed Volumes

1. **Throughput Optimized HDD (st1)**
   - Low-cost HDD volume
   - Baseline throughput of 40 MiB/s
   - Up to 500 MiB/s burst
   - Suitable for frequently accessed data

2. **Cold HDD (sc1)**
   - Lowest cost HDD volume
   - Baseline throughput of 12 MiB/s
   - Up to 80 MiB/s burst
   - Suitable for less frequently accessed data

### EBS Features

#### Snapshots
- Point-in-time copies of EBS volumes
- Stored in S3
- Incremental backups
- Can be used to create new volumes

#### Encryption
- Encryption at rest
- Encryption in flight between instance and volume
- Uses AWS Key Management Service (KMS)

#### Multi-Attach
- Attach same volume to multiple instances
- Only supported with io1/io2 volumes
- Requires cluster-aware file system

### EBS Optimization

#### Provisioned IOPS
- Guarantee specific IOPS performance
- Required for volumes larger than 4 GiB

#### I/O Characteristics
- IOPS: Input/Output Operations Per Second
- Throughput: Data transfer rate (MiB/s)
- Latency: Time for I/O operations

## Amazon EFS (Elastic File System)

### What is Amazon EFS?

Amazon EFS provides a simple, scalable, fully managed elastic NFS file system for use with AWS Cloud services and on-premises resources.

### EFS Storage Classes

1. **Standard**
   - Default storage class
   - Suitable for frequently accessed files

2. **Infrequent Access (IA)**
   - Lower storage cost
   - Higher retrieval costs
   - Automatic lifecycle management

### EFS Performance Modes

1. **General Purpose**
   - Default performance mode
   - Suitable for most use cases

2. **Max I/O**
   - Higher IOPS and throughput
   - Suitable for large datasets

### EFS Throughput Modes

1. **Bursting**
   - Throughput scales with file system size
   - Can burst up to 100 MiB/s per TiB

2. **Provisioned**
   - Specify throughput independent of size
   - Suitable for consistent performance requirements

### EFS Features

- POSIX file system semantics
- File system access from multiple instances
- Automatic scaling
- Encryption at rest

## Amazon FSx

### What is Amazon FSx?

Amazon FSx provides fully managed third-party file systems optimized for a variety of workloads.

### FSx for Windows File Server

- Fully managed Windows file servers
- Support for SMB protocol
- Integration with Active Directory
- Support for Windows ACLs

### FSx for Lustre

- High-performance file system
- Optimized for compute-intensive workloads
- Integration with S3
- Suitable for machine learning and HPC

## AWS Storage Gateway

### What is Storage Gateway?

AWS Storage Gateway connects on-premises environments with cloud storage, providing seamless integration between on-premises IT environments and AWS storage infrastructure.

### Gateway Types

1. **File Gateway**
   - File interface backed by S3
   - Accessed via NFS or SMB

2. **Volume Gateway**
   - Block storage volumes
   - Cached or stored mode

3. **Tape Gateway**
   - Virtual tape library
   - Replaces physical tape infrastructure

### Storage Gateway Features

- Seamless integration with existing applications
- Automatic data transfer to AWS
- Local cache for low-latency access
- Encryption in transit and at rest

## AWS Backup

### What is AWS Backup?

AWS Backup is a fully managed backup service that makes it easy to centralize and automate the backup of data across AWS services.

### Supported Services

- Amazon EBS
- Amazon RDS
- Amazon DynamoDB
- Amazon EFS
- Amazon Storage Gateway

### Backup Features

- Centralized backup management
- Automated backup policies
- Cross-region and cross-account backup
- Backup vaults with encryption
- Audit and compliance reporting
