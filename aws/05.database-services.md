# Database Services

## Amazon RDS (Relational Database Service)

### What is Amazon RDS?

Amazon RDS is a managed relational database service that makes it easy to set up, operate, and scale relational databases in the cloud.

### Supported Database Engines

1. **Amazon Aurora**
   - MySQL and PostgreSQL-compatible
   - Up to 5x faster than MySQL
   - Up to 3x faster than PostgreSQL

2. **MySQL**
   - Open-source relational database
   - Wide community support

3. **PostgreSQL**
   - Advanced open-source database
   - Rich feature set

4. **MariaDB**
   - Community-developed fork of MySQL
   - Enhanced performance and security

5. **Oracle**
   - Enterprise database solution
   - Bring Your Own License (BYOL)

6. **SQL Server**
   - Microsoft's relational database
   - Multiple editions available

### RDS Features

#### Managed Service
- Automated provisioning
- OS patching
- Database software installation
- Ongoing maintenance

#### High Availability
- Multi-AZ deployments
- Automatic failover
- Data redundancy

#### Backup and Recovery
- Automated backups
- Point-in-time recovery
- Manual snapshots

#### Security
- Network isolation with VPC
- Encryption at rest and in transit
- IAM database authentication

#### Monitoring
- Enhanced monitoring
- Performance insights
- CloudWatch integration

### RDS Deployment Options

#### Single-AZ
- Single database instance
- No automatic failover
- Lower cost option

#### Multi-AZ
- Primary and standby instances
- Synchronous replication
- Automatic failover
- High availability

### RDS Read Replicas

- Asynchronous replication
- Offload read traffic
- Can be in different regions
- Up to 5 read replicas per source

## Amazon Aurora

### What is Amazon Aurora?

Amazon Aurora is a MySQL and PostgreSQL-compatible relational database built for the cloud that combines the performance and availability of traditional enterprise databases with the simplicity and cost-effectiveness of open-source databases.

### Aurora Features

#### Performance
- Up to 5x faster than MySQL
- Up to 3x faster than PostgreSQL
- SSD-based storage
- Optimized for cloud

#### Scalability
- Compute scaling without downtime
- Storage automatically scales up to 128 TB
- Up to 15 read replicas

#### Reliability
- 6 copies of data across 3 AZs
- Self-healing storage
- Crash recovery

#### Security
- Encryption at rest and in transit
- IAM integration
- VPC network isolation

### Aurora Replicas

1. **Aurora Replicas**
   - Up to 15 read replicas
   - Automatic failover
   - Shared storage

2. **MySQL/PostgreSQL Read Replicas**
   - Up to 5 read replicas
   - Asynchronous replication
   - Separate storage

## Amazon DynamoDB

### What is Amazon DynamoDB?

Amazon DynamoDB is a fully managed NoSQL database service that provides fast and predictable performance with seamless scalability.

### DynamoDB Features

#### Serverless
- No servers to provision or manage
- Automatic scaling
- Pay per request

#### Performance
- Single-digit millisecond latency
- Built-in caching with DAX

#### Security
- Encryption at rest
- IAM integration
- VPC endpoints

#### Global Tables
- Multi-region replication
- Active-active configuration
- Automatic synchronization

### DynamoDB Core Components

#### Tables
- Collection of items
- Schema flexible
- Primary key required

#### Items
- Group of attributes
- Similar to rows in relational databases

#### Attributes
- Fundamental data elements
- Similar to fields/columns

#### Primary Keys
1. **Partition Key**
   - Unique partition key
   - Distributes data across partitions

2. **Composite Key**
   - Partition key + sort key
   - Enables sorting within partitions

### DynamoDB Operations

#### Data Operations
- **PutItem**: Create or replace item
- **GetItem**: Retrieve single item
- **UpdateItem**: Modify existing item
- **DeleteItem**: Remove item
- **Query**: Retrieve items with same partition key
- **Scan**: Examine all items

#### Capacity Modes
1. **Provisioned**
   - Specify read/write capacity units
   - Pay for provisioned capacity

2. **On-Demand**
   - Pay per request
   - Automatic scaling

### DynamoDB Accelerator (DAX)

- Fully managed in-memory cache
- Microsecond latency
- Compatible with DynamoDB API
- Offloads read traffic

## Amazon Redshift

### What is Amazon Redshift?

Amazon Redshift is a fast, scalable data warehouse that makes it simple and cost-effective to analyze all your data using standard SQL and your existing Business Intelligence (BI) tools.

### Redshift Architecture

#### Leader Node
- Handles client connections
- Receives queries
- Coordinates parallel query execution

#### Compute Nodes
- Execute queries in parallel
- Store data
- Up to 512 nodes

### Redshift Features

#### Performance
- Columnar storage
- Massively parallel processing
- Query optimization
- Result caching

#### Scalability
- Resize clusters
- Add/remove nodes
- Automatic distribution

#### Security
- Encryption at rest and in transit
- VPC isolation
- IAM integration

#### Integration
- Data lake integration
- BI tool compatibility
- JDBC/ODBC drivers

### Redshift Spectrum

- Query data directly in S3
- No data loading required
- Serverless querying
- Exabyte-scale data

## Amazon ElastiCache

### What is Amazon ElastiCache?

Amazon ElastiCache is a fully managed in-memory data store and cache service that supports Redis and Memcached engines.

### Supported Engines

#### Redis
- Advanced data structures
- Pub/Sub messaging
- Multi-AZ with failover
- Backup and restore

#### Memcached
- Simple key-value store
- Horizontal scaling
- Multi-threaded
- No persistence

### ElastiCache Use Cases

- Caching database queries
- Session stores
- Real-time analytics
- Gaming leaderboards
- Chat applications

### ElastiCache Features

#### Performance
- Sub-millisecond latency
- In-memory storage
- Horizontal scaling

#### Management
- Automated setup
- Monitoring and alerts
- Automatic failover

#### Security
- VPC isolation
- IAM integration
- Encryption in transit and at rest

## Amazon Neptune

### What is Amazon Neptune?

Amazon Neptune is a fast, reliable, fully managed graph database service that makes it easy to build and run applications that work with highly connected datasets.

### Neptune Features

#### Graph Models
- Property graph model
- RDF model with SPARQL

#### Performance
- Optimized storage
- High-throughput queries
- Parallel querying

#### Security
- Encryption at rest and in transit
- VPC isolation
- IAM integration

#### Integration
- Gremlin and SPARQL APIs
- TinkerPop and RDF standards

## Amazon DocumentDB

### What is Amazon DocumentDB?

Amazon DocumentDB is a fast, scalable, highly available, and fully managed document database service that supports MongoDB workloads.

### DocumentDB Features

#### Compatibility
- MongoDB 3.6 API
- Existing MongoDB tools and drivers

#### Performance
- SSD storage
- Automatic indexing
- Query optimization

#### Scalability
- Compute and storage scaling
- Up to 15 read replicas

#### Security
- Encryption at rest and in transit
- VPC isolation
- IAM integration

## Amazon Timestream

### What is Amazon Timestream?

Amazon Timestream is a fast, scalable, fully managed time series database service that makes it easy to store and analyze trillions of time series data points per day.

### Timestream Features

#### Purpose-Built
- Optimized for time series data
- Automatic data lifecycle management

#### Scalability
- Serverless
- Automatic scaling

#### Analytics
- Built-in analytics functions
- SQL-like query language

#### Integration
- IoT and operational applications
- Monitoring and observability
