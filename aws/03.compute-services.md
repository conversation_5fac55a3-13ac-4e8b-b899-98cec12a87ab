# Compute Services

## Amazon EC2 (Elastic Compute Cloud)

### What is EC2?

Amazon EC2 provides resizable compute capacity in the cloud. It reduces the time required to obtain and boot new server instances to minutes, allowing you to quickly scale capacity, both up and down, as your computing requirements change.

### EC2 Instance Types

Instance types are grouped into families based on target workload:

1. **General Purpose** (T3, M5, M6)
   - Balance of compute, memory, and networking resources
   - Suitable for diverse workloads

2. **Compute Optimized** (C5, C6)
   - High performance processors
   - Ideal for compute-intensive applications

3. **Memory Optimized** (R5, X1, z1d)
   - High memory-to-CPU ratios
   - Suitable for in-memory databases

4. **Storage Optimized** (I3, D2)
   - High disk throughput
   - Ideal for distributed file systems

5. **Accelerated Computing** (P3, G4, F1)
   - Hardware accelerators (GPUs, FPGAs)
   - Machine learning, graphics processing

### EC2 Pricing Models

1. **On-Demand Instances**
   - Pay by the hour/second with no commitments
   - Perfect for short-term, spiky workloads

2. **Reserved Instances**
   - Up to 72% discount compared to On-Demand
   - Commitment for 1 or 3 years
   - Three types: Standard, Convertible, Scheduled

3. **Spot Instances**
   - Up to 90% discount compared to On-Demand
   - Bid on unused EC2 capacity
   - Can be interrupted by AWS

4. **Dedicated Hosts**
   - Physical EC2 server dedicated for your use
   - Useful for server-bound software licenses

### EC2 Instance Lifecycle

1. **Launch**
   - Create and start an instance
2. **Reboot**
   - Restart a running instance
3. **Stop/Start**
   - Stop and restart an instance (EBS-backed only)
4. **Hibernate**
   - Preserve RAM state to EBS volume
5. **Terminate**
   - Permanently delete an instance

### EC2 Storage Options

1. **Amazon EBS** (Elastic Block Store)
   - Persistent block-level storage
   - Can be attached to instances

2. **Instance Store**
   - Ephemeral storage on the host
   - Lost when instance is stopped/terminated

3. **Amazon EFS** (Elastic File System)
   - Network file system
   - Can be mounted by multiple instances

4. **Amazon S3**
   - Object storage
   - Access via APIs

## Amazon ECS (Elastic Container Service)

### What is ECS?

Amazon ECS is a fully managed container orchestration service that supports Docker containers and allows you to run and scale containerized applications on AWS.

### ECS Components

1. **Clusters**
   - Logical grouping of tasks or services

2. **Tasks**
   - Running containers defined by task definitions

3. **Services**
   - Ensures desired number of tasks are running

4. **Task Definitions**
   - Blueprint for tasks
   - Defines container images, resources, etc.

### ECS Launch Types

1. **EC2 Launch Type**
   - Run containers on EC2 instances
   - More control over infrastructure

2. **Fargate Launch Type**
   - Serverless compute for containers
   - No EC2 instance management

## Amazon EKS (Elastic Kubernetes Service)

### What is EKS?

Amazon EKS is a managed Kubernetes service that makes it easy to run Kubernetes on AWS without needing to install and operate your own Kubernetes control plane or worker nodes.

### EKS Benefits

- Fully managed Kubernetes control plane
- Integrates with IAM for authentication
- Integrates with other AWS services
- Supports both EC2 and Fargate

## AWS Lambda

### What is Lambda?

AWS Lambda is a serverless compute service that runs your code in response to events and automatically manages the underlying compute resources.

### Lambda Features

- **Event-driven**: Responds to triggers from other AWS services
- **Serverless**: No server management required
- **Auto-scaling**: Scales automatically with incoming requests
- **Pay-per-use**: Charged only for execution time

### Lambda Use Cases

- Data processing
- Real-time file processing
- Web applications
- Automation
- Chatbots

### Lambda Limits

- **Execution timeout**: 15 minutes
- **Memory allocation**: 128 MB to 10,240 MB
- **Deployment package**: 50 MB (zipped)
- **Ephemeral disk**: 10 GB (/tmp directory)

## AWS Batch

### What is AWS Batch?

AWS Batch enables developers, scientists, and engineers to easily and efficiently run hundreds of thousands of batch computing jobs on AWS.

### Batch Components

1. **Jobs**
   - Unit of work submitted to AWS Batch

2. **Job Definitions**
   - Template for jobs
   - Specifies container image, resources, etc.

3. **Job Queues**
   - Holds jobs waiting to be scheduled

4. **Compute Environments**
   - Managed or unmanaged pools of EC2 instances

## Amazon Lightsail

### What is Lightsail?

Amazon Lightsail is designed to be the easiest way to get started with AWS for developers who need to build websites or web applications.

### Lightsail Features

- Preconfigured virtual machines
- Bundled with applications
- Simplified management interface
- Predictable monthly pricing

## AWS Elastic Beanstalk

### What is Elastic Beanstalk?

AWS Elastic Beanstalk is an easy-to-use service for deploying and scaling web applications and services developed with Java, .NET, PHP, Node.js, Python, Ruby, Go, and Docker.

### Elastic Beanstalk Benefits

- Platform as a Service (PaaS)
- Handles deployment, capacity provisioning, load balancing, and auto-scaling
- Retains full control over AWS resources
- Supports multiple programming languages

## AWS Outposts
n### What is AWS Outposts?

AWS Outposts brings native AWS services, infrastructure, and operating models to virtually any data center, co-location space, or on-premises facility.

### Outposts Benefits

- Low-latency access to on-premises systems
- Local data processing
- Data residency requirements
- Hybrid applications

## AWS Wavelength

### What is AWS Wavelength?

AWS Wavelength embeds AWS compute and storage services within the telecommunications providers' data centers at the edge of the 5G networks.

### Wavelength Benefits

- Ultra-low latency applications
- Mobile edge computing
- Enhanced user experience for 5G applications
