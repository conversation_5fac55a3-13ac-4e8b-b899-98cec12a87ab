# Monitoring and Management

## Amazon CloudWatch

### What is Amazon CloudWatch?

Amazon CloudWatch is a monitoring and observability service that provides data and actionable insights to monitor applications, respond to system-wide performance changes, optimize resource utilization, and get a unified view of operational health.

### CloudWatch Components

#### Metrics
- Time-ordered set of data points
- Published to CloudWatch every minute
- Standard and custom metrics
- Namespace, dimension, timestamp, unit, statistic

#### Alarms
- Watch a single metric
- Perform actions based on metric's value
- Three states: OK, ALARM, INSUFFICIENT_DATA
- Notification through SNS

#### Dashboards
- Create customized views of metrics
- Share dashboards with others
- Support various widgets
- Cross-region and cross-account

#### Logs
- Monitor and troubleshoot systems
- Centralized log management
- Real-time analysis
- Integration with Lambda

#### Events (Amazon EventBridge)
- Respond to state changes
- Schedule automated actions
- Event-driven computing
- Default event bus and custom event buses

### CloudWatch Metrics

#### Standard Metrics
- CPU utilization
- Network in/out
- Disk read/write
- Status check failed

#### Custom Metrics
- Application-level metrics
- Business metrics
- Publish using PutMetricData API
- 15 months retention

#### High Resolution Metrics
- Sub-minute frequency
- 1-second intervals
- More responsive alarms
- Higher cost

### CloudWatch Alarms

#### Alarm States
- **OK**: Metric is within threshold
- **ALARM**: Metric is outside threshold
- **INSUFFICIENT_DATA**: Not enough data

#### Alarm Actions
- Auto Scaling actions
- EC2 actions
- SNS notifications
- Multiple actions per state

#### Composite Alarms
- Combine multiple alarms
- AND/OR logic
- Reduce alarm noise
- Complex alerting logic

### CloudWatch Logs

#### Log Concepts
- **Log groups**: Group related log streams
- **Log streams**: Sequence of log events from source
- **Log events**: Timestamp and raw message

#### Log Features
- Real-time monitoring
- Log retention settings
- Metric filters
- Export to S3
- Integration with Lambda

#### CloudWatch Logs Insights
- Interactive log analytics
- Query language for logs
- Fast querying at scale
- Visualization of results

### CloudWatch Agent

#### What is CloudWatch Agent?
- Collect metrics and logs from EC2 instances
- Collect system-level metrics
- Collect logs from applications
- On-premises server support

#### Agent Configuration
- JSON configuration file
- Specify metrics and logs to collect
- Define log groups and streams
- Set collection intervals

## AWS CloudTrail

### What is AWS CloudTrail?

AWS CloudTrail is a service that enables governance, compliance, operational auditing, and risk auditing of your AWS account.

### CloudTrail Features

#### Event History
- Last 90 days of account activity
- Read-only access to events
- Search and filter capabilities

#### Trails
- Configuration for delivering logs
- Apply to all regions or single region
- Log file integrity validation
- CloudWatch Logs integration

#### Event Types
- **Management events**: Control plane operations
- **Data events**: Data plane operations
- **Insight events**: Unusual activity detection

#### CloudTrail Insights
- Detect unusual activity
- Machine learning based
- Reduce manual analysis
- Automatic alerting

## AWS Config

### What is AWS Config?

AWS Config is a service that enables you to assess, audit, and evaluate the configurations of your AWS resources.

### AWS Config Features

#### Configuration History
- Point-in-time snapshots
- Configuration changes over time
- Relationship between resources

#### Configuration Recorder
- Record configuration changes
- Specify resource types
- Global resource recording

#### Rules
- Evaluate resource configurations
- Pre-built and custom rules
- Remediation actions
- Compliance status

#### Conformance Packs
- Bundle of Config rules and remediation actions
- Deploy as code
- Cross-account and cross-region

### AWS Config Use Cases

- Security analysis
- Change management
- Compliance auditing
- Operational troubleshooting

## AWS Systems Manager (SSM)

### What is AWS Systems Manager?

AWS Systems Manager gives you visibility and control of your infrastructure on AWS and provides a unified user interface for operational data and automated actions.

### Systems Manager Capabilities

#### Operational Insights
- Resource groups
- Insights dashboards
- Trusted advisor integration

#### Automation
- Common maintenance tasks
- Runbooks for operations
- Cross-account and cross-region

#### Parameter Store
- Store configuration data
- Secure string parameters
- Hierarchical organization
- Integration with other services

#### Patch Manager
- Automate patching
- Patch compliance reporting
- Custom patch baselines
- Cross-platform support

#### Run Command
- Remote command execution
- No SSH or RDP required
- Audit and compliance
- Parallel execution

#### Session Manager
- Secure shell access
- No open inbound ports
- Session logging
- Cross-platform support

#### Maintenance Windows
- Define time periods for actions
- Control when operations occur
- Reduce impact on users
- Schedule tasks

## Amazon EventBridge

### What is Amazon EventBridge?

Amazon EventBridge is a serverless event bus service that makes it easy to connect applications using data from external sources.

### EventBridge Components

#### Event Buses
- Default event bus
- Custom event buses
- Partner event buses

#### Rules
- Match events
- Define targets
- Transform events

#### Targets
- Services that receive events
- Lambda functions, SQS queues, etc.
- Multiple targets per rule

#### Event Patterns
- JSON patterns to match events
- Content-based filtering
- Prefix matching

### EventBridge Use Cases

- Application integration
- Microservices architecture
- Serverless applications
- Infrastructure automation

## AWS X-Ray
n### What is AWS X-Ray?

AWS X-Ray helps developers analyze and debug production, distributed applications, such as those built using a microservices architecture.

### X-Ray Components

#### Segments
- Compute blocks of work
- Trace ID for tracking
- Subsegments for detailed view

#### Traces
- End-to-end path of request
- Collection of segments
- Trace ID correlation

#### Service Maps
- Visual representation of services
- Request paths
- Performance bottlenecks

### X-Ray Features

#### Tracing
- Automatic and manual instrumentation
- SDK support for multiple languages
- Integration with AWS services

#### Analytics
- Filter expressions
- Grouping and aggregation
- Time-based analysis

#### Integration
- EC2, Lambda, ECS, EKS
- API Gateway, ELB
- Third-party integrations

## AWS Trusted Advisor
n### What is AWS Trusted Advisor?

AWS Trusted Advisor provides real-time guidance to help you provision your resources following AWS best practices.

### Trusted Advisor Checks

#### Cost Optimization
- Idle resources
- Reserved instance optimization
- Savings plan recommendations

#### Performance
- Service limits
- Provisioned throughput
- Compute optimizer

#### Security
- MFA on root account
- IAM policies
- Security groups

#### Fault Tolerance
- Backup procedures
- Availability Zone distribution
- Redundancy

#### Service Limits
- Resource usage
- Service quotas
- Limit increase requests

### Trusted Advisor Features

- Priority levels
- Check summaries
- Action recommendations
- Weekly refresh

## AWS Control Tower
n### What is AWS Control Tower?

AWS Control Tower provides the easiest way to set up and govern a secure, multi-account AWS environment based on best practices.

### Control Tower Components

#### Landing Zone
- Multi-account environment
- Baseline guardrails
- Account factory

#### Guardrails
- Preventive controls
- Detective controls
- Pre-packaged rules

#### Account Factory
- Automated account provisioning
- Standardized account creation
- Integration with Service Catalog

### Control Tower Benefits

- Automated setup
- Best practice blueprints
- Ongoing compliance
- Centralized auditing

## AWS Management Console
n### Console Features

#### Unified Interface
- Single sign-on
- Resource management
- Service integration

#### Mobile Application
- Access from mobile devices
- Critical alerts
- Basic resource management

#### Command Line Interface
- Programmatic access
- Scripting capabilities
- Automation support

## AWS Command Line Interface (CLI)

### CLI Features

#### Command Structure
- aws <service> <command>
- Common options
- Service-specific commands

#### Configuration
- AWS credentials
- Default region
- Output format
- Named profiles

#### Automation
- Scripting support
- Batch operations
- Integration with shell

## AWS Software Development Kits (SDKs)

### SDK Benefits

- Language-specific libraries
- Consistent APIs
- Error handling
- Authentication

### Supported Languages
- Python (Boto3)
- Java
- JavaScript
- .NET
- Go
- PHP
- Ruby

## AWS Well-Architected Tool
n### What is the Well-Architected Tool?

AWS Well-Architected Tool helps you review the state of your workloads and compares them to the latest AWS architectural best practices.

### Well-Architected Pillars

1. **Operational Excellence**
2. **Security**
3. **Reliability**
4. **Performance Efficiency**
5. **Cost Optimization**
6. **Sustainability**

### Tool Features

- Workload reviews
- Lens support
- Custom lenses
- Integration with other services
