# Cloud Fundamentals & AWS Core Concepts

## What is Cloud Computing?

Cloud computing is the delivery of computing services over the internet, including servers, storage, databases, networking, software, and analytics. This model allows for faster innovation, flexible resources, and economies of scale.

### Key Characteristics of Cloud Computing

1. **On-demand self-service**: Users can provision computing capabilities as needed automatically
2. **Broad network access**: Services are available over the network and accessed through standard mechanisms
3. **Resource pooling**: Provider's computing resources are pooled to serve multiple consumers
4. **Rapid elasticity**: Capabilities can be elastically provisioned and released
5. **Measured service**: Resource usage can be monitored, controlled, and reported

## Cloud Service Models

### Infrastructure as a Service (IaaS)

- Provides virtual machines and infrastructure components
- Examples: Amazon EC2, Google Compute Engine
- User manages: Applications, data, runtime, middleware, OS
- Provider manages: Virtualization, servers, storage, networking

### Platform as a Service (PaaS)

- Provides platform for application development and deployment
- Examples: AWS Elastic Beanstalk, Google App Engine
- User manages: Applications, data
- Provider manages: Runtime, middleware, OS, virtualization, servers, storage, networking

### Software as a Service (SaaS)

- Delivers software applications over the internet
- Examples: Salesforce, Google Workspace, Microsoft 365
- User manages: User data
- Provider manages: Applications, data, runtime, middleware, OS, virtualization, servers, storage, networking

## Cloud Deployment Models

### Public Cloud

- Services offered over the public internet
- Owned and operated by third-party cloud service providers
- Cost effective and scalable

### Private Cloud

- Exclusive use by a single organization
- Can be hosted on-premises or by third parties
- More control and security

### Hybrid Cloud

- Combination of public and private clouds
- Data and applications can move between environments
- Flexibility and optimization of existing infrastructure

### Community Cloud

- Shared by several organizations with common concerns
- May be hosted on-premises or by third parties

## AWS Global Infrastructure

### Regions

- Geographical locations where AWS has data centers
- Each region is completely independent
- Example: US East (N. Virginia), US West (Oregon), EU (Ireland)

### Availability Zones (AZs)

- One or more discrete data centers within a region
- Each AZ has redundant power, networking, and connectivity
- Physically separated for fault isolation
- Connected with high bandwidth, ultra-low latency networking

### Edge Locations

- Endpoints for AWS used for caching content
- Consist of CloudFront distribution points
- Located in major cities worldwide

## AWS Core Services Overview

### Compute

- Amazon EC2 (Elastic Compute Cloud)
- AWS Lambda (Serverless computing)
- Amazon ECS (Elastic Container Service)
- Amazon EKS (Elastic Kubernetes Service)

### Storage

- Amazon S3 (Simple Storage Service)
- Amazon EBS (Elastic Block Store)
- Amazon EFS (Elastic File System)

### Database

- Amazon RDS (Relational Database Service)
- Amazon DynamoDB (NoSQL database)
- Amazon Redshift (Data warehouse)

### Networking

- Amazon VPC (Virtual Private Cloud)
- AWS Direct Connect
- Amazon Route 53 (DNS service)

## Benefits of Cloud Computing

### Cost Savings

- No upfront infrastructure costs
- Pay-as-you-go pricing model
- Reduced ongoing maintenance costs

### Scalability

- Scale up or down based on demand
- Automatic scaling capabilities

### Reliability

- Data backup, disaster recovery, and data replication
- High availability through multiple AZs

### Security

- Advanced security capabilities
- Compliance certifications
- Data encryption

### Agility

- Faster deployment of applications
- Quick access to resources

## AWS Well-Architected Framework

### Five Pillars

1. **Operational Excellence**
   - Ability to run and monitor systems
   - Continuous improvement of supporting processes and procedures

2. **Security**
   - Protection of information and systems
   - Identity and access management
   - Data protection

3. **Reliability**
   - Ability to prevent and quickly recover from failures
   - Recovery planning and testing

4. **Performance Efficiency**
   - Efficient use of computing resources
   - Maintain efficiency as demand changes

5. **Cost Optimization**
   - Avoid unnecessary costs
   - Maximize benefits of cloud technologies

### Best Practices

- Design for failure
- Decouple components
- Implement elasticity
- Think parallel
- Use shared resources
