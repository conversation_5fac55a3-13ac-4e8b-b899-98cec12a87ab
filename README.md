# DevOps Learning Notes

This repository contains comprehensive notes and documentation for various DevOps topics, focusing on Linux, Docker, Cloud computing, and related technologies.

## Linux
0. [Basic Commands](linux/00.basic-commands.md)
1. [File Ownership and Permissions Management](linux/01.chown+chmod.md)
2. [Process Management Commands](linux/02.process-management.md)
3. [Text Processing Commands](linux/03.text-processing.md)
4. [Packages Management](linux/04.packages-management.md)
5. [Network and Security](linux/05.network-and-security.md)
6. [Automation and Scripting](linux/06.automation-and-scripting.md)
7. [Shell Scripting for DevOps](linux/07.shell.md)

## Networking
0. [Basic Concepts](network/00.basic-concepts.md)
1. [LAN, MAN, WAN](network/01.LAN-MAN-WAN.md)
2. [OSI Model](network/02.OSI-model.md)
3. [Subnet](network/03.Subnet.md)
4. [CIDR](network/04.CIDR.md)
5. [Network with Linux](network/05.network-with-linux.md)

## Docker
0. [Fundamentals](docker/00.fundamentals.md)
1. [Core Commands](docker/01.core-commands.md)
2. [Dockerfile Mastery](docker/02.dockerfile-mastery.md)
3. [Networking](docker/03.networking.md)
4. [Data Management](docker/04.data-management.md)
5. [Multi-Container Applications](docker/05.multi-container-applications.md)
6. [Production & Advanced Topics](docker/06.production-advanced.md)
7. [DevOps Integration](docker/07.devops-integration.md)
8. [Security Best Practices](docker/08.security-best-practices.md)
9. [Resource Management](docker/09.resource-management.md)

## Cloud Computing

*(Coming soon)*

## CI/CD
0. [Foundation Concepts](ci-cd/00.foundation-concepts.md)
1. [Core Principles](ci-cd/01.core-principles.md)
2. [CI/CD Platforms](ci-cd/02.ci-cd-platforms.md)
3. [Configuration and Scripting](ci-cd/03.configuration-scripting.md)
4. [Advanced Topics](ci-cd/04.advanced-topics.md)
5. [DevOps Integration](ci-cd/05.devops-integration.md)
6. [Best Practices](ci-cd/06.best-practices.md)

## Kubernetes
0. [Foundation Concepts](kubernetes/00.foundation-concepts.md)
1. [Core Workload Resources](kubernetes/01.core-workload-resources.md)
2. [Networking](kubernetes/02.networking.md)
3. [Storage](kubernetes/03.storage.md)
4. [Configuration and Security](kubernetes/04.configuration-security.md)
5. [Advanced Workloads](kubernetes/05.advanced-workloads.md)
6. [Monitoring and Troubleshooting](kubernetes/06.monitoring-troubleshooting.md)
7. [Advanced Topics](kubernetes/07.advanced-topics.md)
8. [Production Readiness](kubernetes/08.production-readiness.md)
9. [High Availability Systems](kubernetes/09.high-availability.md)
10. [Auto-scaling](kubernetes/10.auto-scaling.md)
11. [Ingress](kubernetes/11.ingress.md)

## Monitoring and Logging

*(Coming soon)*

## Infrastructure as Code (IaC)

*(Coming soon)*

## Container Orchestration

*(Coming soon)*

---

*This repository is actively maintained and updated with new content regularly.*