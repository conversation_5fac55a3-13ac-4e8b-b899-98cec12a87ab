# DevOps Learning Notes

This repository contains comprehensive notes and documentation for various DevOps topics, focusing on Linux, Docker, Cloud computing, and related technologies.

## Linux
0. [Basic Commands](linux/00.basic-commands.md)
1. [File Ownership and Permissions Management](linux/01.chown+chmod.md)
2. [Process Management Commands](linux/02.process-management.md)
3. [Text Processing Commands](linux/03.text-processing.md)
4. [Packages Management](linux/04.packages-management.md)
5. [Network and Security](linux/05.network-and-security.md)
6. [Automation and Scripting](linux/06.automation-and-scripting.md)
7. [Shell Scripting for DevOps](linux/07.shell.md)

## Networking
0. [Basic Concepts](network/00.basic-concepts.md)
1. [LAN, MAN, WAN](network/01.LAN-MAN-WAN.md)
2. [OSI Model](network/02.OSI-model.md)
3. [Subnet](network/03.Subnet.md)
4. [CIDR](network/04.CIDR.md)
5. [Network with Linux](network/05.network-with-linux.md)

## Docker

*(Coming soon)*

## Cloud Computing

*(Coming soon)*

## CI/CD

*(Coming soon)*

## Monitoring and Logging

*(Coming soon)*

## Infrastructure as Code (IaC)

*(Coming soon)*

## Container Orchestration

*(Coming soon)*

---

*This repository is actively maintained and updated with new content regularly.*