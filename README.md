# DevOps Learning Notes

This repository contains comprehensive notes and documentation for various DevOps topics, focusing on Linux, Docker, Cloud computing, and related technologies.

## Linux
0. [Basic Commands](linux/00.basic-commands.md)
1. [File Ownership and Permissions Management](linux/01.chown+chmod.md)
2. [Process Management Commands](linux/02.process-management.md)
3. [Text Processing Commands](linux/03.text-processing.md)
4. [Packages Management](linux/04.packages-management.md)
5. [Network and Security](linux/05.network-and-security.md)
6. [Automation and Scripting](linux/06.automation-and-scripting.md)
7. [Shell Scripting for DevOps](linux/07.shell.md)

## Networking
0. [Basic Concepts](network/00.basic-concepts.md)
1. [LAN, MAN, WAN](network/01.LAN-MAN-WAN.md)
2. [OSI Model](network/02.OSI-model.md)
3. [Subnet](network/03.Subnet.md)
4. [CIDR](network/04.CIDR.md)
5. [Network with Linux](network/05.network-with-linux.md)

## Docker
0. [Fundamentals](docker/00.fundamentals.md)
1. [Core Commands](docker/01.core-commands.md)
2. [Dockerfile Mastery](docker/02.dockerfile-mastery.md)
3. [Networking](docker/03.networking.md)
4. [Data Management](docker/04.data-management.md)
5. [Multi-Container Applications](docker/05.multi-container-applications.md)
6. [Production & Advanced Topics](docker/06.production-advanced.md)
7. [DevOps Integration](docker/07.devops-integration.md)
8. [Security Best Practices](docker/08.security-best-practices.md)
9. [Resource Management](docker/09.resource-management.md)

## Cloud Computing

*(Coming soon)*

## CI/CD

*(Coming soon)*

## Monitoring and Logging

*(Coming soon)*

## Infrastructure as Code (IaC)

*(Coming soon)*

## Container Orchestration

*(Coming soon)*

---

*This repository is actively maintained and updated with new content regularly.*