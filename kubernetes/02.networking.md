# Kubernetes Networking

Networking in Kubernetes is a critical aspect that enables communication between pods, services, and external clients. Understanding Kubernetes networking concepts is essential for deploying and managing applications effectively.

## Cluster Networking

Kubernetes networking follows several fundamental principles:

1. **Pod-to-Pod Communication**: All pods can communicate with each other without NAT
2. **Node-to-Pod Communication**: Nodes can communicate with all pods without NAT
3. **Pod-to-Service Communication**: Pods can communicate with services
4. **External-to-Service Communication**: External clients can communicate with services

### Container Network Interface (CNI)

Kubernetes uses CNI plugins to implement networking. Popular CNI plugins include:
- Calico
- Flannel
- Cilium
- Weave Net

### Pod Networking

Each pod receives a unique IP address within the cluster network:

```bash
# View pod IPs
kubectl get pods -o wide

# Access one pod from another
kubectl exec -it pod1 -- ping <pod2-ip>
```

## Service Types

Services provide stable endpoints for accessing applications. There are four main service types:

### ClusterIP

Default service type that exposes the service on a cluster-internal IP:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: clusterip-service
spec:
  selector:
    app: myapp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
```

### NodePort

Exposes the service on each node's IP at a static port:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: nodeport-service
spec:
  selector:
    app: myapp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
      nodePort: 30007
  type: NodePort
```

### LoadBalancer

Exposes the service externally using a cloud provider's load balancer:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: loadbalancer-service
spec:
  selector:
    app: myapp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: LoadBalancer
```

### ExternalName

Maps a service to an external DNS name:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: external-service
spec:
  type: ExternalName
  externalName: api.example.com
```

## Ingress

Ingress manages external access to services, typically HTTP/HTTPS routes:

### Ingress Controller

An ingress controller is required to satisfy an Ingress. Popular options include:
- NGINX Ingress Controller
- Traefik
- AWS Load Balancer Controller

### Ingress Resource Example

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: example-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: myapp.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-service
            port:
              number: 80
      - path: /
        pathType: Prefix
        backend:
          service:
            name: web-service
            port:
              number: 80
```

### TLS Configuration

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tls-ingress
spec:
  tls:
  - hosts:
    - myapp.example.com
    secretName: myapp-tls
  rules:
  - host: myapp.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: web-service
            port:
              number: 80
```

## Network Policies

Network policies specify how groups of pods are allowed to communicate with each other and other network endpoints.

### Default Deny All Ingress

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-ingress
spec:
  podSelector: {}
  policyTypes:
  - Ingress
```

### Allow Specific Traffic

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-api-ingress
spec:
  podSelector:
    matchLabels:
      app: api
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: web
    ports:
    - protocol: TCP
      port: 8080
```

### Network Policy Operations

```bash
# Apply a network policy
kubectl apply -f network-policy.yaml

# List network policies
kubectl get networkpolicies

# Describe a network policy
kubectl describe networkpolicy allow-api-ingress
```

Understanding these networking concepts is crucial for securing and exposing applications in Kubernetes clusters.
