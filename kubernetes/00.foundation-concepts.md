# Kubernetes Foundation Concepts

Understanding the fundamental concepts of Kubernetes is essential for effective cluster management and application deployment. This guide covers the core principles that form the foundation of Kubernetes.

## Container Fundamentals

Before diving into Kubernetes, it's important to understand container technology, primarily Docker.

### Docker Basics

Docker is a containerization platform that packages applications and their dependencies into lightweight, portable containers.

#### Key Docker Commands

```bash
# Pull an image from Docker Hub
docker pull nginx:latest

# Run a container in detached mode
docker run -d --name web-server -p 8080:80 nginx:latest

# List running containers
docker ps

# View container logs
docker logs web-server

# Execute a command in a running container
docker exec -it web-server /bin/bash
```

### Images and Containers

- **Images**: Read-only templates used to create containers
- **Containers**: Runnable instances of images
- **Registries**: Storage for images (e.g., Docker Hub, private registries)

## What is Kubernetes?

Kubernetes is an open-source container orchestration platform that automates deployment, scaling, and management of containerized applications across clusters of hosts.

### Key Features

- **Service Discovery**: Automatic detection of services and their endpoints
- **Load Balancing**: Distribution of network traffic across multiple pods
- **Storage Orchestration**: Automatic mounting of storage systems
- **Self-healing**: Automatic restart of failed containers
- **Secret and Configuration Management**: Secure handling of sensitive information
- **Batch Execution**: Management of batch and CI workloads

## Core Components

Kubernetes architecture consists of a control plane (master) and worker nodes.

### Control Plane Components

1. **API Server**: The front-end for the Kubernetes control plane
2. **etcd**: Consistent and highly-available key-value store
3. **Scheduler**: Assigns pods to nodes
4. **Controller Manager**: Runs controller processes

### Worker Node Components

1. **kubelet**: Agent that ensures containers are running in a pod
2. **kube-proxy**: Network proxy that maintains network rules
3. **Container Runtime**: Software responsible for running containers (e.g., Docker, containerd)

## Pods

A pod is the smallest deployable unit in Kubernetes, representing a single instance of an application.

### Pod Lifecycle

1. **Pending**: Pod has been accepted but containers aren't running yet
2. **Running**: Pod bound to a node and all containers running
3. **Succeeded**: All containers terminated successfully
4. **Failed**: All containers terminated and at least one failed
5. **Unknown**: Pod state could not be obtained

### Multi-container Pods

Pods can contain multiple containers that share the same network namespace and storage volumes.

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: multi-container-pod
spec:
  containers:
  - name: web
    image: nginx
  - name: sidecar
    image: busybox
    command: ['sh', '-c', 'while true; do echo "Sidecar running"; sleep 30; done']
```

## Nodes

Nodes are worker machines in Kubernetes that run applications as pods.

### Node Status

Each node contains information about its status:

- **Addresses**: Hostname, external IP, internal IP
- **Conditions**: Ready, MemoryPressure, DiskPressure, PIDPressure, NetworkUnavailable
- **Capacity**: Available resources (CPU, memory, pods)
- **Info**: General information about the node

### Node Management

```bash
# List all nodes
kubectl get nodes

# Get detailed information about a node
kubectl describe node <node-name>

# Mark a node as unschedulable
kubectl cordon <node-name>

# Evict pods from a node
kubectl drain <node-name>
```
