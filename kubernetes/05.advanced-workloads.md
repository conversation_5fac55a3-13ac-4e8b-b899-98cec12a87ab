# Advanced Kubernetes Workloads

Beyond basic Deployments and Services, Kubernetes provides specialized workload resources for specific use cases. Understanding these advanced workloads is crucial for deploying complex applications.

## StatefulSets

StatefulSets manage stateful applications with stable network identities and persistent storage.

### Key Features

- **Stable Network Identity**: Each pod gets a persistent hostname
- **Ordered Deployment**: Pods are created sequentially
- **Ordered Termination**: Pods are terminated in reverse order
- **Persistent Storage**: Volume persistence across pod rescheduling

### StatefulSet Example

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: web
spec:
  selector:
    matchLabels:
      app: nginx
  serviceName: "nginx"
  replicas: 3
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx
        ports:
        - containerPort: 80
          name: web
        volumeMounts:
        - name: www
          mountPath: /usr/share/nginx/html
  volumeClaimTemplates:
  - metadata:
      name: www
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 1Gi
```

### StatefulSet Operations

```bash
# Create a StatefulSet
kubectl apply -f statefulset.yaml

# Scale a StatefulSet
kubectl scale statefulset web --replicas=5

# Delete a StatefulSet
kubectl delete statefulset web

# View StatefulSet status
kubectl get statefulsets
```

## DaemonSets

DaemonSets ensure that a copy of a pod runs on all or selected nodes.

### Use Cases

- **Logging**: Collecting logs from all nodes
- **Monitoring**: Running monitoring agents
- **Networking**: Managing network plugins
- **Storage**: Running storage daemons

### DaemonSet Example

```yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluentd-elasticsearch
  namespace: kube-system
  labels:
    k8s-app: fluentd-logging
spec:
  selector:
    matchLabels:
      name: fluentd-elasticsearch
  template:
    metadata:
      labels:
        name: fluentd-elasticsearch
    spec:
      tolerations:
      - key: node-role.kubernetes.io/master
        effect: NoSchedule
      containers:
      - name: fluentd-elasticsearch
        image: quay.io/fluentd_elasticsearch/fluentd:v2.5.2
        resources:
          limits:
            memory: 200Mi
          requests:
            cpu: 100m
            memory: 200Mi
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
```

### DaemonSet Operations

```bash
# List DaemonSets
kubectl get daemonsets -n kube-system

# Describe a DaemonSet
kubectl describe daemonset fluentd-elasticsearch -n kube-system

# Update a DaemonSet
kubectl apply -f daemonset.yaml
```

## Jobs and CronJobs

Jobs and CronJobs manage batch processing workloads.

### Job Example

```yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: pi
spec:
  template:
    spec:
      containers:
      - name: pi
        image: perl
        command: ["perl",  "-Mbignum=bpi", "-wle", "print bpi(2000)"]
      restartPolicy: Never
  backoffLimit: 4
```

### CronJob Example

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: hello
spec:
  schedule: "*/1 * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: hello
            image: busybox
            imagePullPolicy: IfNotPresent
            command:
            - /bin/sh
            - -c
            - date; echo Hello from the Kubernetes cluster
          restartPolicy: OnFailure
```

### Job Operations

```bash
# Create a Job
kubectl apply -f job.yaml

# List Jobs
kubectl get jobs

# View Job logs
kubectl logs job/pi

# Delete a Job
kubectl delete job pi
```

## Horizontal Pod Autoscaler (HPA)

HPA automatically scales the number of pods based on observed CPU utilization or custom metrics.

### HPA Example

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: php-apache
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: php-apache
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50
```

### HPA Operations

```bash
# Create an HPA
kubectl apply -f hpa.yaml

# List HPAs
kubectl get hpa

# Describe an HPA
kubectl describe hpa php-apache

# Delete an HPA
kubectl delete hpa php-apache
```

These advanced workload resources enable Kubernetes to handle complex application deployment scenarios.
