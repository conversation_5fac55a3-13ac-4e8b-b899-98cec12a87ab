# Kubernetes High Availability Systems

Building highly available systems in Kubernetes is essential for production environments. This guide covers the key concepts and practices for designing and implementing high availability in Kubernetes clusters.

## Multi-Zone Deployments

Distributing workloads across multiple availability zones protects against zone-level failures.

### Pod Topology Spread Constraints

Use topology spread constraints to ensure pods are distributed across zones:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 6
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: topology.kubernetes.io/zone
        whenUnsatisfiable: DoNotSchedule
        labelSelector:
          matchLabels:
            app: nginx
      containers:
      - name: nginx
        image: nginx
```

### Node Affinity and Anti-Affinity

Use node affinity to control pod placement:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: topology.kubernetes.io/zone
                operator: In
                values:
                - us-west-2a
                - us-west-2b
                - us-west-2c
      containers:
      - name: nginx
        image: nginx
```

## Pod Disruption Budgets

PodDisruptionBudgets (PDBs) ensure a minimum number of pods remain available during voluntary disruptions.

### PDB Example

```yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: nginx-pdb
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: nginx
```

Or using maxUnavailable:

```yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: nginx-pdb
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app: nginx
```

### PDB Operations

```bash
# Create a PDB
kubectl apply -f pdb.yaml

# List PDBs
kubectl get pdb

# Describe a PDB
kubectl describe pdb nginx-pdb
```

## Health Checks and Readiness Probes

Proper health checking ensures traffic is only sent to healthy pods.

### Liveness, Readiness, and Startup Probes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx
        ports:
        - containerPort: 80
        # Liveness probe checks if the container is running
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        # Readiness probe checks if the container is ready to serve traffic
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        # Startup probe for slow starting containers
        startupProbe:
          httpGet:
            path: /healthz
            port: 8080
          failureThreshold: 30
          periodSeconds: 10
```

## Load Balancing Strategies

Kubernetes provides several load balancing mechanisms for high availability.

### Service Load Balancing

```yaml
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer
```

### External Traffic Policy

Control how external traffic is handled:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer
  externalTrafficPolicy: Local  # Preserve client source IP
```

## Backup and Disaster Recovery

Implementing backup and disaster recovery strategies is crucial for high availability.

### etcd Backup

```bash
# Backup etcd
ETCDCTL_API=3 etcdctl --endpoints=https://127.0.0.1:2379 \
  --cacert=/etc/kubernetes/pki/etcd/ca.crt \
  --cert=/etc/kubernetes/pki/etcd/server.crt \
  --key=/etc/kubernetes/pki/etcd/server.key \
  snapshot save /tmp/etcd-snapshot.db

# Verify backup
ETCDCTL_API=3 etcdctl --write-out=table snapshot status /tmp/etcd-snapshot.db
```

### Velero for Kubernetes Resources

```yaml
# Schedule regular backups
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: daily-backup
spec:
  schedule: "0 1 * * *"  # Daily at 1 AM
  template:
    includedNamespaces:
    - '*'
    excludedNamespaces:
    - kube-system
    snapshotVolumes: true
    ttl: 168h0m0s  # 7 days
```

```bash
# Install Velero
velero install \
  --provider aws \
  --plugins velero/velero-plugin-for-aws:v1.2.0 \
  --bucket velero-backups \
  --backup-location-config region=us-west-2 \
  --snapshot-location-config region=us-west-2

# Create a backup
velero create backup nginx-backup --selector app=nginx

# Restore from backup
velero create restore nginx-restore --from-backup nginx-backup
```

## Best Practices

1. **Distribute workloads**: Use topology spread constraints to distribute pods across failure domains
2. **Set appropriate PDBs**: Ensure minimum availability during maintenance
3. **Implement comprehensive health checks**: Use liveness, readiness, and startup probes
4. **Regular backups**: Implement automated backup solutions for etcd and applications
5. **Test disaster recovery**: Regularly test your backup and restore procedures
6. **Monitor and alert**: Implement monitoring for key metrics and set appropriate alerts

High availability in Kubernetes requires careful planning and implementation of these concepts to ensure your applications remain available even during failures.
