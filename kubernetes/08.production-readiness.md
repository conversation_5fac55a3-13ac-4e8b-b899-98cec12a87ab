# Kubernetes Production Readiness

Preparing Kubernetes clusters for production requires careful planning and implementation of best practices. This guide covers essential topics for production-ready Kubernetes environments.

## Cluster Setup

Proper cluster setup is fundamental for production environments.

### Kubeadm Setup

Kubeadm simplifies Kubernetes cluster creation:

```bash
# Initialize the control plane
kubeadm init --pod-network-cidr=**********/16

# Configure kubectl for the user
mkdir -p $HOME/.kube
sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
sudo chown $(id -u):$(id -g) $HOME/.kube/config

# Apply a pod network
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml

# Join worker nodes
kubeadm join <control-plane-host>:<control-plane-port> --token <token> --discovery-token-ca-cert-hash sha256:<hash>
```

### Managed Services

Cloud providers offer managed Kubernetes services:

1. **Amazon EKS**: AWS managed Kubernetes
2. **Google GKE**: Google managed Kubernetes
3. **Azure AKS**: Microsoft managed Kubernetes

### EKS Example

```yaml
# eks-cluster.yaml
apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig
metadata:
  name: production-cluster
  region: us-west-2
nodeGroups:
  - name: ng-1
    instanceType: m5.large
    desiredCapacity: 3
    minSize: 2
    maxSize: 5
    iam:
      withAddonPolicies:
        autoScaler: true
```

```bash
# Create EKS cluster
eksctl create cluster -f eks-cluster.yaml
```

## Backup and Disaster Recovery

Protecting cluster data is essential for production environments.

### etcd Backup

```bash
# Backup etcd
ETCDCTL_API=3 etcdctl --endpoints=https://127.0.0.1:2379 \
  --cacert=/etc/kubernetes/pki/etcd/ca.crt \
  --cert=/etc/kubernetes/pki/etcd/server.crt \
  --key=/etc/kubernetes/pki/etcd/server.key \
  snapshot save /tmp/etcd-snapshot.db

# Verify backup
ETCDCTL_API=3 etcdctl --write-out=table snapshot status /tmp/etcd-snapshot.db
```

### etcd Restore

```bash
# Stop etcd and kube-apiserver
systemctl stop etcd
systemctl stop kube-apiserver

# Restore from backup
ETCDCTL_API=3 etcdctl snapshot restore /tmp/etcd-snapshot.db \
  --data-dir=/var/lib/etcd-backup \
  --initial-cluster=master-1=https://192.168.1.101:2380 \
  --initial-advertise-peer-urls=https://192.168.1.101:2380

# Replace data directory
mv /var/lib/etcd /var/lib/etcd-old
mv /var/lib/etcd-backup /var/lib/etcd

# Start services
systemctl start etcd
systemctl start kube-apiserver
```

### Velero for Backup

Velero provides backup and disaster recovery for Kubernetes resources and volumes.

```yaml
# backup.yaml
apiVersion: velero.io/v1
kind: Backup
metadata:
  name: daily-backup
  labels:
    velero.io/storage-location: default
spec:
  includedNamespaces:
  - '*'
  excludedNamespaces:
  - kube-system
  includedResources:
  - '*'
  excludedResources:
  - nodes
  - events
  - events.events.k8s.io
  labelSelector:
    matchLabels:
      backup: daily
  snapshotVolumes: true
  ttl: 168h0m0s
```

```bash
# Install Velero
velero install \
  --provider aws \
  --plugins velero/velero-plugin-for-aws:v1.2.0 \
  --bucket velero-backups \
  --backup-location-config region=us-west-2 \
  --snapshot-location-config region=us-west-2

# Create a backup
velero create backup daily-backup --selector backup=daily

# Restore from backup
velero create restore daily-restore --from-backup daily-backup
```

## Upgrades

Regular upgrades ensure security and access to new features.

### Cluster Upgrade Process

```bash
# Check current versions
kubectl version

# Check available versions
kubeadm upgrade plan

# Upgrade control plane
kubeadm upgrade apply v1.21.0

# Upgrade kubelet on control plane node
sudo apt-get update && sudo apt-get install -y kubelet=1.21.0-00 kubectl=1.21.0-00
sudo systemctl daemon-reload
sudo systemctl restart kubelet

# Upgrade worker nodes
kubectl drain <node-to-drain> --ignore-daemonsets
# On worker node:
kubeadm upgrade node
sudo apt-get update && sudo apt-get install -y kubelet=1.21.0-00 kubectl=1.21.0-00
sudo systemctl daemon-reload
sudo systemctl restart kubelet
kubectl uncordon <node-to-drain>
```

### EKS Upgrade

```bash
# Update EKS cluster version
eksctl upgrade cluster --name=production-cluster --version=1.21

# Update node groups
eksctl upgrade nodegroup --name=ng-1 --cluster=production-cluster
```

## Performance Tuning

Optimizing performance ensures efficient resource utilization.

### Resource Optimization

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: optimized-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: optimized-app
  template:
    metadata:
      labels:
        app: optimized-app
    spec:
      containers:
      - name: app
        image: myapp:latest
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Node Tuning

```yaml
apiVersion: tuning.kubeflow.org/v1alpha1
kind: NodeTuning
metadata:
  name: performance-profile
spec:
  profile:
    name: performance
    cpu:
      isolated: "2-7"
      reserved: "0-1"
    hugepages:
      defaultHugepagesSize: 2M
      pages:
        - size: 2M
          count: 512
```

## Cost Optimization

Efficient resource utilization reduces operational costs.

### Resource Monitoring

```bash
# View resource usage
kubectl top nodes
kubectl top pods

# Identify over-provisioned resources
kubectl get pods -o custom-columns=NAME:.metadata.name,REQ_CPU:.spec.containers[*].resources.requests.cpu,LIM_CPU:.spec.containers[*].resources.limits.cpu

# Identify unused persistent volumes
kubectl get pv -o custom-columns=NAME:.metadata.name,CAPACITY:.spec.capacity.storage,STATUS:.status.phase
```

### Right-Sizing

```yaml
apiVersion: v1
kind: LimitRange
metadata:
  name: limit-range
spec:
  limits:
  - default:
      cpu: 200m
      memory: 256Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    type: Container
```

### Spot Instances

```yaml
# eks-cluster-spot.yaml
apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig
metadata:
  name: spot-cluster
  region: us-west-2
nodeGroups:
  - name: spot-ng
    instanceTypes: ["m5.large", "m5a.large", "m5n.large"]
    spot: true
    desiredCapacity: 3
    minSize: 2
    maxSize: 10
```

### Horizontal Pod Autoscaler

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cost-optimized-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cost-optimized-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

Production readiness requires careful attention to these aspects to ensure reliable, secure, and efficient Kubernetes environments.
