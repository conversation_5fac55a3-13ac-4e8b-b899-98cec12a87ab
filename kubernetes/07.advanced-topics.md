# Advanced Kubernetes Topics

As you advance in your Kubernetes journey, understanding these advanced topics becomes crucial for managing complex environments and extending Kubernetes functionality.

## <PERSON><PERSON> is the package manager for Kubernetes, enabling you to define, install, and manage Kubernetes applications as charts.

### Helm Chart Structure

```
mychart/
  Chart.yaml
  values.yaml
  charts/
  templates/
    deployment.yaml
    service.yaml
    ingress.yaml
```

### Chart.yaml Example

```yaml
apiVersion: v2
name: myapp
description: A Helm chart for Kubernetes
version: 0.1.0
appVersion: 1.0.0
```

### values.yaml Example

```yaml
replicaCount: 3

image:
  repository: nginx
  tag: stable
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80
```

### Helm Commands

```bash
# Add a Helm repository
helm repo add stable https://charts.helm.sh/stable

# Search for charts
helm search repo nginx

# Install a chart
helm install my-release stable/nginx

# Install a chart with custom values
helm install my-release . --values values.yaml

# Upgrade a release
helm upgrade my-release . --values values.yaml

# List releases
helm list

# Uninstall a release
helm uninstall my-release
```

## Custom Resource Definitions (CRDs)

CRDs extend the Kubernetes API to define custom resources.

### CRD Example

```yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: databases.example.com
spec:
  group: example.com
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              replicas:
                type: integer
              version:
                type: string
  scope: Namespaced
  names:
    plural: databases
    singular: database
    kind: Database
```

### Custom Resource Example

```yaml
apiVersion: example.com/v1
kind: Database
metadata:
  name: my-database
spec:
  replicas: 3
  version: "12.4"
```

### CRD Operations

```bash
# Apply a CRD
kubectl apply -f crd.yaml

# List CRDs
kubectl get crds

# Describe a CRD
kubectl describe crd databases.example.com

# List custom resources
kubectl get databases
```

## Operators

Operators are controllers that manage applications using Kubernetes APIs.

### Operator Example

An Operator typically includes:
1. **CRDs** to define custom resources
2. **Controller** to manage custom resources
3. **RBAC** for permissions

### Prometheus Operator Example

```yaml
apiVersion: monitoring.coreos.com/v1
kind: Prometheus
metadata:
  name: prometheus
spec:
  serviceAccountName: prometheus
  serviceMonitorSelector:
    matchLabels:
      team: frontend
  resources:
    requests:
      memory: 400Mi
```

### Operator Lifecycle

1. **Installation**: Deploy the Operator
2. **Configuration**: Create custom resources
3. **Management**: Operator reconciles desired state
4. **Updates**: Operator handles upgrades

## Multi-Cluster Management

Managing multiple Kubernetes clusters requires specialized tools and strategies.

### Cluster Federation

Kubernetes Federation enables managing multiple clusters as a single entity.

### Tools for Multi-Cluster Management

1. **Rancher**: Complete container management platform
2. **Kubefed**: Kubernetes Cluster Federation
3. **Anthos**: Google's hybrid and multi-cloud platform
4. **OpenShift**: Red Hat's Kubernetes platform

### Multi-Cluster Deployment Example

```yaml
apiVersion: federation.kf.io/v1alpha1
kind: FederatedDeployment
metadata:
  name: nginx
spec:
  template:
    metadata:
      labels:
        app: nginx
    spec:
      replicas: 3
      selector:
        matchLabels:
          app: nginx
      template:
        metadata:
          labels:
            app: nginx
        spec:
          containers:
          - name: nginx
            image: nginx:1.14.2
            ports:
            - containerPort: 80
```

## GitOps

GitOps is a way to manage infrastructure and applications using Git as the source of truth.

### Principles

1. **Declarative**: Desired system state is stored in Git
2. **Automated**: Changes are automatically applied
3. **Auditable**: All changes are versioned
4. **Recoverable**: System can be restored from Git

### Tools

1. **Argo CD**: Declarative GitOps CD for Kubernetes
2. **Flux**: Continuous delivery solution
3. **Tekton**: CI/CD framework

### Argo CD Example

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: guestbook
spec:
  project: default
  source:
    repoURL: https://github.com/argoproj/argocd-example-apps.git
    targetRevision: HEAD
    path: guestbook
  destination:
    server: https://kubernetes.default.svc
    namespace: guestbook
```

Understanding these advanced topics enables you to extend Kubernetes functionality and manage complex environments effectively.
