# Core Kubernetes Workload Resources

Kubernetes provides several built-in resources for deploying and managing applications. Understanding these core workload resources is essential for effective application management.

## Deployments

Deployments manage the desired state of ReplicaSets and provide declarative updates for applications.

### Key Features

- **Declarative Updates**: Describe desired state rather than specific actions
- **Rolling Updates**: Gradually replace pods with new versions
- **Rollbacks**: Revert to previous versions when needed
- **Scaling**: Adjust the number of replica pods

### Deployment Example

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.14.2
        ports:
        - containerPort: 80
```

### Common Deployment Operations

```bash
# Create a deployment
kubectl apply -f deployment.yaml

# Scale a deployment
kubectl scale deployment nginx-deployment --replicas=5

# Update a deployment (if image version changed in YAML)
kubectl apply -f deployment.yaml

# Rollback to previous version
kubectl rollout undo deployment nginx-deployment

# Check deployment status
kubectl rollout status deployment nginx-deployment

# View deployment history
kubectl rollout history deployment nginx-deployment
```

## ReplicaSets

ReplicaSets ensure a specified number of pod replicas are running at any given time.

### Relationship to Deployments

Deployments manage ReplicaSets, which in turn manage pods. Direct use of ReplicaSets is uncommon.

```yaml
apiVersion: apps/v1
kind: ReplicaSet
metadata:
  name: nginx-replicaset
  labels:
    app: nginx
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.14.2
        ports:
        - containerPort: 80
```

## Services

Services provide stable networking endpoints for accessing applications running in pods.

### Service Types

1. **ClusterIP**: Internal service (default)
2. **NodePort**: Expose service on each node's IP
3. **LoadBalancer**: External load balancer
4. **ExternalName**: Map to external service

### Service Example

```yaml
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
```

### Service Operations

```bash
# Create a service
kubectl apply -f service.yaml

# List services
kubectl get services

# Get detailed service information
kubectl describe service nginx-service

# Access service from within cluster
kubectl run -it --rm debug --image=curlimages/curl -- sh
# Inside the pod:
curl http://nginx-service
```

## ConfigMaps and Secrets

### ConfigMaps

ConfigMaps store non-sensitive configuration data as key-value pairs.

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  database_url: postgres://localhost:5432
  log_level: info
```

Using ConfigMaps in Pods:

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: configmap-pod
spec:
  containers:
    - name: app
      image: myapp
      env:
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: database_url
```

### Secrets

Secrets store sensitive information like passwords, tokens, and keys.

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: db-secret
type: Opaque
data:
  username: bXl1c2Vy
  password: bXlwYXNzd29yZA==
```

Using Secrets in Pods:

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: secret-pod
spec:
  containers:
    - name: app
      image: myapp
      env:
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: username
```

## Namespaces

Namespaces provide a mechanism for isolating groups of resources within a cluster.

### Namespace Operations

```bash
# List namespaces
kubectl get namespaces

# Create a namespace
kubectl create namespace development

# Deploy resources to a specific namespace
kubectl apply -f deployment.yaml -n development

# Set default namespace for context
kubectl config set-context --current --namespace=development
```

### Namespace Example

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: production
```

Namespaces are particularly useful for:
- **Resource Quotas**: Limit resource consumption per namespace
- **Access Control**: Apply RBAC policies per namespace
- **Organization**: Separate environments (dev, test, prod)
