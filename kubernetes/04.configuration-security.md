# Kubernetes Configuration and Security

Security and configuration management are critical aspects of running production Kubernetes clusters. This guide covers essential topics for securing and configuring Kubernetes environments.

## RBAC (Role-Based Access Control)

RBAC regulates access to cluster resources based on user roles.

### RBAC Components

1. **Role/ClusterRole**: Defines permissions
2. **RoleBinding/ClusterRoleBinding**: Grants permissions to users

### Role Example

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: default
  name: pod-reader
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "watch", "list"]
```

### Role Binding Example

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: read-pods
  namespace: default
subjects:
- kind: User
  name: jane
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: pod-reader
  apiGroup: rbac.authorization.k8s.io
```

### Cluster Role Example

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: secret-reader
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "watch", "list"]
```

### RBAC Operations

```bash
# List roles
kubectl get roles

# List role bindings
kubectl get rolebindings

# Describe a role
kubectl describe role pod-reader

# Check permissions
kubectl auth can-i list pods --namespace default --as jane
```

## Security Contexts

Security contexts define privilege and access control settings for pods and containers.

### Pod Security Context

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: security-context-pod
spec:
  securityContext:
    runAsUser: 1000
    runAsGroup: 3000
    fsGroup: 2000
  containers:
  - name: app
    image: nginx
    securityContext:
      allowPrivilegeEscalation: false
```

### Container Security Context

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: container-security-context
spec:
  containers:
  - name: app
    image: nginx
    securityContext:
      runAsUser: 1000
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
        add:
        - NET_BIND_SERVICE
```

## Resource Quotas and Limits

Resource management ensures fair allocation and prevents resource exhaustion.

### Resource Quota

```yaml
apiVersion: v1
kind: ResourceQuota
metadata:
  name: quota-example
spec:
  hard:
    requests.cpu: "1"
    requests.memory: 1Gi
    limits.cpu: "2"
    limits.memory: 2Gi
    pods: "10"
    services: "5"
```

### Limit Range

```yaml
apiVersion: v1
kind: LimitRange
metadata:
  name: limit-range-example
spec:
  limits:
  - default:
      cpu: 200m
      memory: 512Mi
    defaultRequest:
      cpu: 100m
      memory: 256Mi
    type: Container
```

### Pod with Resource Requests and Limits

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: resource-pod
spec:
  containers:
  - name: app
    image: nginx
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
```

## Pod Security Standards

Pod Security Standards provide a replacement for Pod Security Policies.

### Pod Security Admission

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: restricted-namespace
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/enforce-version: latest
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
```

### Pod Security Levels

1. **Privileged**: Unrestricted policy
2. **Baseline**: Minimally restrictive policy
3. **Restricted**: Heavily restricted policy

### Example of a Restricted Pod

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: restricted-pod
spec:
  containers:
  - name: app
    image: nginx
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsNonRoot: true
      seccompProfile:
        type: RuntimeDefault
```

Proper configuration and security practices are essential for maintaining a secure and well-managed Kubernetes environment.
