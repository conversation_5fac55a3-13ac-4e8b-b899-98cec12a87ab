# Kubernetes Storage

Storage management in Kubernetes is essential for persisting data beyond the lifecycle of individual pods. Understanding storage concepts ensures data durability and availability for stateful applications.

## Volumes

Volumes provide a way to persist data beyond the lifecycle of containers and share data between containers in a pod.

### Volume Types

Common volume types include:
- **emptyDir**: Temporary storage on the node
- **hostPath**: Mounts a file or directory from the node
- **configMap**: Injects configuration data
- **secret**: Injects sensitive data
- **persistentVolumeClaim**: References a Persistent Volume

### Volume Example

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: volume-pod
spec:
  containers:
  - name: app
    image: nginx
    volumeMounts:
    - name: html-volume
      mountPath: /usr/share/nginx/html
  volumes:
  - name: html-volume
    emptyDir: {}
```

## Persistent Volumes (PV)

Persistent Volumes are cluster resources that provide storage independent of pod lifecycle.

### PV Example

```yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-example
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: manual
  hostPath:
    path: /mnt/data
```

### PV Access Modes

- **ReadWriteOnce (RWO)**: Read-write by a single node
- **ReadOnlyMany (ROX)**: Read-only by many nodes
- **ReadWriteMany (RWX)**: Read-write by many nodes
- **ReadWriteOncePod (RWOP)**: Read-write by a single pod

## Persistent Volume Claims (PVC)

PVCs are requests for storage by users, similar to how pods request compute resources.

### PVC Example

```yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pvc-example
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: manual
```

### Using PVCs in Pods

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: pvc-pod
spec:
  containers:
  - name: app
    image: nginx
    volumeMounts:
    - name: data-volume
      mountPath: /usr/share/nginx/html
  volumes:
  - name: data-volume
    persistentVolumeClaim:
      claimName: pvc-example
```

## Storage Classes

Storage Classes enable dynamic provisioning of Persistent Volumes.

### Storage Class Example

```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp2
  fsType: ext4
```

### Dynamic Provisioning with Storage Classes

```yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: dynamic-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
```

### Storage Class Operations

```bash
# List storage classes
kubectl get storageclass

# Describe a storage class
kubectl describe storageclass fast-ssd

# Create a storage class
kubectl apply -f storage-class.yaml
```

## StatefulSets and Storage

StatefulSets provide stable storage for stateful applications:

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: web
spec:
  selector:
    matchLabels:
      app: nginx
  serviceName: "nginx"
  replicas: 3
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx
        ports:
        - containerPort: 80
          name: web
        volumeMounts:
        - name: www
          mountPath: /usr/share/nginx/html
  volumeClaimTemplates:
  - metadata:
      name: www
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 1Gi
```

Understanding storage concepts is crucial for deploying stateful applications in Kubernetes.
