# Kubernetes Auto-scaling

Auto-scaling in Kubernetes allows you to automatically adjust the number of pods and nodes based on demand. This guide covers the different types of auto-scaling mechanisms available in Kubernetes.

## Horizontal Pod Autoscaler (HPA)

HPA automatically scales the number of pods in a deployment, replication controller, or replica set based on observed CPU utilization or custom metrics.

### Basic HPA Example

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: php-apache
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: php-apache
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50
```

### HPA with Memory Metrics

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: memory-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-deployment
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
```

### HPA with Custom Metrics

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: custom-metric-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-deployment
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: 100
```

### HPA Operations

```bash
# Create an HPA
kubectl apply -f hpa.yaml

# List HPAs
kubectl get hpa

# Describe an HPA
kubectl describe hpa php-apache

# Delete an HPA
kubectl delete hpa php-apache
```

## Vertical Pod Autoscaler (VPA)

VPA automatically sets resource requests and limits for containers based on historical usage.

### VPA Recommender

```yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: hamster-vpa
spec:
  targetRef:
    apiVersion: "apps/v1"
    kind: Deployment
    name: hamster
  updatePolicy:
    updateMode: "Auto"  # Options: Auto, Initial, Off
  resourcePolicy:
    containerPolicies:
    - containerName: hamster
      maxAllowed:
        cpu: 1000m
        memory: 1Gi
      minAllowed:
        cpu: 100m
        memory: 50Mi
```

### VPA Modes

1. **Auto**: Automatically applies recommendations to pods
2. **Initial**: Applies recommendations only at pod creation
3. **Off**: Only provides recommendations, doesn't apply them

### VPA Operations

```bash
# Install VPA (if not already installed)
kubectl apply -f https://github.com/kubernetes/autoscaler/raw/master/vertical-pod-autoscaler/deploy/vpa-rbac.yaml
kubectl apply -f https://github.com/kubernetes/autoscaler/raw/master/vertical-pod-autoscaler/deploy/vpa-crds.yaml
kubectl apply -f https://github.com/kubernetes/autoscaler/raw/master/vertical-pod-autoscaler/deploy/vpa-v1-crd.yaml
kubectl apply -f https://github.com/kubernetes/autoscaler/raw/master/vertical-pod-autoscaler/deploy/vpa-v1.yaml

# Create a VPA
kubectl apply -f vpa.yaml

# List VPAs
kubectl get vpa

# Describe a VPA
kubectl describe vpa hamster-vpa
```

## Cluster Autoscaler

Cluster Autoscaler automatically adjusts the size of the Kubernetes cluster based on resource requests.

### AWS Cluster Autoscaler

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cluster-autoscaler
  namespace: kube-system
  labels:
    app: cluster-autoscaler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cluster-autoscaler
  template:
    metadata:
      labels:
        app: cluster-autoscaler
    spec:
      serviceAccountName: cluster-autoscaler
      containers:
      - image: k8s.gcr.io/autoscaling/cluster-autoscaler:v1.21.0
        name: cluster-autoscaler
        resources:
          limits:
            cpu: 100m
            memory: 300Mi
          requests:
            cpu: 100m
            memory: 300Mi
        command:
        - ./cluster-autoscaler
        - --v=4
        - --stderrthreshold=info
        - --cloud-provider=aws
        - --skip-nodes-with-local-storage=false
        - --expander=least-waste
        - --node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/<cluster-name>
        env:
        - name: AWS_REGION
          value: us-west-2
        volumeMounts:
        - name: ssl-certs
          mountPath: /etc/ssl/certs/ca-certificates.crt
          readOnly: true
        imagePullPolicy: "Always"
      volumes:
      - name: ssl-certs
        hostPath:
          path: "/etc/ssl/certs/ca-bundle.crt"
```

### Cluster Autoscaler with EKS

```yaml
# eks-cluster-autoscaler.yaml
apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig
metadata:
  name: autoscaling-cluster
  region: us-west-2
nodeGroups:
  - name: ng-1
    instanceType: m5.large
    desiredCapacity: 2
    minSize: 1
    maxSize: 10
    iam:
      withAddonPolicies:
        autoScaler: true
```

### Cluster Autoscaler Operations

```bash
# Install Cluster Autoscaler on EKS
eksctl create cluster -f eks-cluster-autoscaler.yaml

# List nodes
kubectl get nodes

# View Cluster Autoscaler logs
kubectl logs -n kube-system -l app=cluster-autoscaler

# Check Cluster Autoscaler status
kubectl get configmap cluster-autoscaler-status -n kube-system -o yaml
```

## Custom Metrics for Scaling

Custom metrics allow you to scale based on application-specific metrics.

### Metrics Server Installation

```bash
# Install Metrics Server
kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml

# Verify installation
kubectl get deployment metrics-server -n kube-system
```

### Prometheus Adapter for Custom Metrics

```yaml
# prometheus-adapter-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: adapter-config
  namespace: custom-metrics
data:
  config.yaml: |
    rules:
    - seriesQuery: 'http_requests_total{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^(.*)_total"
        as: "${1}_per_second"
      metricsQuery: 'sum(rate(<<.Series>>{<<.LabelMatchers>>}[2m])) by (<<.GroupBy>>)'
```

### Custom Metrics HPA Example

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: custom-metrics-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: web-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Object
    object:
      metric:
        name: requests-per-second
      describedObject:
        apiVersion: v1
        kind: Service
        name: web-app-svc
      target:
        type: Value
        value: 100
```

## Best Practices and Common Pitfalls

### Best Practices

1. **Start with reasonable min/max values**: Set appropriate min and max replicas based on your application's needs
2. **Use multiple metrics**: Combine CPU, memory, and custom metrics for more accurate scaling
3. **Monitor scaling events**: Set up alerts for scaling activities
4. **Test under load**: Simulate traffic to verify your autoscaling configuration
5. **Consider scaling delays**: Account for the time it takes to scale up/down
6. **Use appropriate thresholds**: Set utilization targets that provide headroom for spikes

### Common Pitfalls

1. **Overscaling**: Setting thresholds too low can cause unnecessary scaling
2. **Thrashing**: Frequent scaling up and down due to inappropriate thresholds
3. **Ignoring cooldown periods**: Not accounting for application startup time
4. **Not monitoring custom metrics**: Relying only on CPU/memory metrics
5. **Inadequate resource limits**: Not setting proper resource requests/limits
6. **Scaling too slowly**: Not scaling fast enough to handle traffic spikes

Properly configured auto-scaling ensures your applications can handle varying loads while optimizing resource utilization.
