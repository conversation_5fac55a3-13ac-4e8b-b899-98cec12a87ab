# Kubernetes Monitoring and Troubleshooting

Effective monitoring and troubleshooting are essential for maintaining healthy Kubernetes clusters and applications. This guide covers essential tools and techniques for diagnosing and resolving issues.

## kubectl Commands

Mastering kubectl is crucial for Kubernetes troubleshooting.

### Basic Commands

```bash
# Get cluster information
kubectl cluster-info

# Get node information
kubectl get nodes

# Get pod information
kubectl get pods

# Get detailed pod information
kubectl describe pod <pod-name>

# Get pod logs
kubectl logs <pod-name>

# Execute commands in a pod
kubectl exec -it <pod-name> -- /bin/bash
```

### Advanced Commands

```bash
# Get events sorted by timestamp
kubectl get events --sort-by=.metadata.creationTimestamp

# Get resource usage for nodes
kubectl top nodes

# Get resource usage for pods
kubectl top pods

# Port forward to a pod
kubectl port-forward pod/<pod-name> 8080:80

# Copy files to/from a pod
kubectl cp <file> <pod-name>:<path>
```

### Debugging Commands

```bash
# Check if a resource can be created
kubectl auth can-i create pods

# View cluster configuration
kubectl config view

# View current context
kubectl config current-context

# Switch context
kubectl config use-context <context-name>
```

## Logging

Effective logging is essential for understanding application behavior and diagnosing issues.

### Pod Logs

```bash
# Get logs from a pod
kubectl logs <pod-name>

# Get logs from a specific container in a multi-container pod
kubectl logs <pod-name> -c <container-name>

# Get logs from previous container instance
kubectl logs <pod-name> --previous

# Stream logs in real-time
kubectl logs -f <pod-name>

# Get logs from all pods with a label
kubectl logs -l app=nginx
```

### Log Aggregation

Centralized logging solutions:
- **ELK Stack**: Elasticsearch, Logstash, Kibana
- **EFK Stack**: Elasticsearch, Fluentd, Kibana
- **Loki**: Lightweight log aggregation by Grafana

### Fluentd Configuration Example

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: kube-system
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      read_from_head true
      <parse>
        @type json
        time_format %Y-%m-%dT%H:%M:%S.%NZ
      </parse>
    </source>
    <match kubernetes.var.log.containers.**nginx**.log>
      @type elasticsearch
      logstash_format true
      host elasticsearch
      port 9200
    </match>
```

## Monitoring

Monitoring provides visibility into cluster and application performance.

### Prometheus

Prometheus is a popular monitoring solution for Kubernetes.

#### ServiceMonitor Example

```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: example-app
  labels:
    team: frontend
spec:
  selector:
    matchLabels:
      app: example-app
  endpoints:
  - port: web
```

### Grafana

Grafana provides visualization for monitoring data.

#### Dashboard Example

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard
  labels:
    grafana_dashboard: "1"
data:
  dashboard.json: |
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true
          }
        ]
      },
      "title": "Kubernetes Cluster Metrics",
      "panels": [
        {
          "type": "graph",
          "title": "CPU Usage",
          "targets": [
            {
              "expr": "rate(container_cpu_usage_seconds_total[5m])",
              "legendFormat": "{{pod}}"
            }
          ]
        }
      ]
    }
```

## Health Checks

Health checks ensure applications are running correctly.

### Liveness, Readiness, and Startup Probes

```yaml
apiVersion: v1
kind: Pod
metadata:
  labels:
    test: liveness
  name: liveness-http
spec:
  containers:
  - name: liveness
    image: k8s.gcr.io/liveness
    args:
    - /server
    livenessProbe:
      httpGet:
        path: /healthz
        port: 8080
      initialDelaySeconds: 3
      periodSeconds: 3
    readinessProbe:
      httpGet:
        path: /ready
        port: 8080
      initialDelaySeconds: 5
      periodSeconds: 5
    startupProbe:
      httpGet:
        path: /healthz
        port: 8080
      failureThreshold: 30
      periodSeconds: 10
```

### Probe Types

1. **Liveness Probe**: Determines if the container is running
2. **Readiness Probe**: Determines if the container is ready to serve traffic
3. **Startup Probe**: Determines if the application within the container has started

## Debugging Techniques

### Common Debugging Scenarios

1. **Pod Stuck in Pending State**
   - Check resource quotas
   - Check node capacity
   - Check taints and tolerations

2. **Pod Stuck in CrashLoopBackOff**
   - Check pod logs
   - Check container image
   - Check resource limits

3. **Service Not Accessible**
   - Check service selectors
   - Check pod labels
   - Check network policies

### Debugging Commands

```bash
# Check pod events
kubectl describe pod <pod-name>

# Check node conditions
kubectl describe node <node-name>

# Check resource quotas
kubectl describe resourcequota

# Check network policies
kubectl get networkpolicies

# Validate configuration
kubectl apply --dry-run=client -f <file.yaml>
```

Effective monitoring and troubleshooting practices are essential for maintaining production Kubernetes environments.
